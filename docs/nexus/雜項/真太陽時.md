
### 中式命理太陽時計算原理

傳統命理學中，太陽時的計算通常遵循以下幾個步驟：

1. **取得真太陽時的基準點**：古代曆法通常以中原地區（例如河南洛陽、開封等地）的經度為基準。我們可以取一個代表性的經度，例如**東經 114 度**。
    
2. **計算當地與基準點的經度差**：這一步是計算你所在位置與中原基準點的經度差異。
    
    - **經度差 = 當地經度 - 中原基準經度**
        
3. **計算時間偏差**：地球自轉每 15 度經度差約等於 1 小時（或每 1 度經度差約等於 4 分鐘）。這個時間偏差就是經度差所造成的。
    
    - **時間偏差（分鐘） = 經度差 × 4**
        
4. **計算當地真太陽時**：將當地使用的標準時間（例如 UTC+8）加上這個時間偏差，並進行均時差的修正。
    
    - **真太陽時 = 標準時間 + 經度修正 + 均時差**
        

---

### 在 Flutter 中實現修正後的計算

基於上述原理，我們可以調整之前的程式碼，以符合中式命理的計算方式。

Dart

```
import 'package:flutter/material.dart';
import 'dart:math';

// 假設經緯度是固定的，這裡以台北市為例
const double latitude = 25.0330;
const double longitude = 121.5654;

// 中原地區的基準經度，通常取東經 114 度
const double zhongyuanLongitude = 114.0;

class TrueSolarTimeWidget extends StatefulWidget {
  @override
  _TrueSolarTimeWidgetState createState() => _TrueSolarTimeWidgetState();
}

class _TrueSolarTimeWidgetState extends State<TrueSolarTimeWidget> {
  String _trueSolarTime = '計算中...';
  
  //... 保持 calculateJulianDay 和 calculateEquationOfTime 函數不變 ...
  double _calculateJulianDay(DateTime date) {
    // 參照之前的函數，這裡省略程式碼
    double year = date.year.toDouble();
    double month = date.month.toDouble();
    double day = date.day.toDouble();
    double hour = date.hour.toDouble();
    double minute = date.minute.toDouble();
    double second = date.second.toDouble();
    
    double fractionalDay = hour / 24 + minute / 1440 + second / 86400;

    if (month <= 2) {
      year -= 1;
      month += 12;
    }
    
    double A = (year / 100).floor().toDouble();
    double B = 2 - A + (A / 4).floor().toDouble();

    return (365.25 * (year + 4716)).floor() + (30.6001 * (month + 1)).floor() + day + B - 1524.5 + fractionalDay;
  }
  
  double _calculateEquationOfTime(double julianDay) {
    // 參照之前的函數，這裡省略程式碼
    double n = julianDay - 2451545.0; 
    
    double L = 280.460 + 0.9856474 * n; 
    while (L > 360) L -= 360;
    while (L < 0) L += 360;
    
    double M = 357.528 + 0.9856003 * n; 
    while (M > 360) M -= 360;
    while (M < 0) M += 360;
    
    double M_rad = M * pi / 180;
    double L_rad = L * pi / 180;
    
    double eqt = -12.277 * sin(M_rad) + 0.165 * sin(2 * M_rad) - 0.287 * cos(2 * L_rad) - 0.101 * sin(2 * L_rad);
    
    return eqt; 
  }

  void _updateTrueSolarTime() {
    DateTime now = DateTime.now();
    double julianDay = _calculateJulianDay(now);
    double equationOfTime = _calculateEquationOfTime(julianDay);
    
    // 計算與中原基準經度的經度差
    double longitudeDifference = longitude - zhongyuanLongitude;
    
    // 計算經度修正的時間（以分鐘為單位）
    double longitudeCorrection = longitudeDifference * 4;
    
    // 將當前時間（本地時間）轉換為總分鐘數
    double totalMinutes = now.hour * 60 + now.minute + now.second / 60;
    
    // 計算真太陽時的總分鐘數
    // 這裡的「now」包含了時區的影響，所以我們直接在這個基礎上加上經度修正
    double trueSolarTotalMinutes = totalMinutes + longitudeCorrection + equationOfTime;
    
    // 處理分鐘數超過 1440 (24小時) 的情況
    int trueSolarHour = (trueSolarTotalMinutes / 60).floor() % 24;
    int trueSolarMinute = (trueSolarTotalMinutes % 60).floor();
    int trueSolarSecond = ((trueSolarTotalMinutes % 1) * 60).round();
    
    setState(() {
      _trueSolarTime = '${trueSolarHour.toString().padLeft(2, '0')}:${trueSolarMinute.toString().padLeft(2, '0')}:${trueSolarSecond.toString().padLeft(2, '0')}';
    });
  }
  
  //... 保持 build 函數不變 ...
  @override
  void initState() {
    super.initState();
    _updateTrueSolarTime();
    // 每秒更新一次時間
    // Timer.periodic(Duration(seconds: 1), (Timer t) => _updateTrueSolarTime());
  }
  
  @override
  Widget build(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Text(
            '當前本地時間:',
            style: TextStyle(fontSize: 18),
          ),
          Text(
            '${DateTime.now().hour.toString().padLeft(2, '0')}:${DateTime.now().minute.toString().padLeft(2, '0')}:${DateTime.now().second.toString().padLeft(2, '0')}',
            style: TextStyle(fontSize: 32, fontWeight: FontWeight.bold),
          ),
          SizedBox(height: 20),
          Text(
            '當前中式真太陽時:',
            style: TextStyle(fontSize: 18),
          ),
          Text(
            _trueSolarTime,
            style: TextStyle(fontSize: 32, fontWeight: FontWeight.bold),
          ),
        ],
      ),
    );
  }
}
```

這個修正後的版本，將計算基準從時區的標準經線**（東經 15° × 時區數）** 變更為一個固定的**中原經度（東經 114°）**，更符合傳統命理學對太陽時的定義。

如果您需要根據使用者當前位置動態獲取經緯度，您可以使用 `geolocator` 或 `location` 這類套件來實現。