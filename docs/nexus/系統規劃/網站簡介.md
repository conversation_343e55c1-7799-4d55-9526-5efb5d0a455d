# 🔮 玄學網站簡介文件

## 一、網站定位

本網站是一個專注於 **命理占卜查詢與互動抽牌** 的現代玄學平台，核心理念為：

> **選擇比努力更重要。**

使用者可透過直觀的卡牌操作與自助式占卜系統，快速獲得針對選擇的指引與建議。

---

## 二、語言與付費機制

### 🌐 多語言支援

- 網站為 **多國語言介面版本**
- 預設語言：**簡體中文**
- 支援語言切換（如：繁體中文、英文，未來可擴充）

### 💰 金流與收費方式

- 整合 **綠界金流（ECPay）**，支援台灣用戶付款
- **查詢類內容免費**（如塔羅說明、卦辭、籤詩原文）
- **抽牌互動與 AI 解讀功能為付費項目**
- 支援一次購買、儲值點數或訂閱制（未來視需求）

---

## 三、網站功能總覽

### 1. 📚 命理查詢資料庫（免費）

| 命理系統         | 提供內容                              |
|------------------|---------------------------------------|
| **塔羅牌**       | 78 張正逆位牌義說明                    |
| **盧恩符文**     | 24 字母含義與象徵                      |
| **星座**         | 十二星座性格、行星屬性、要素說明        |
| **周易卦辭爻辭** | 64 卦的卦辭、爻辭、彖辭與象傳            |
| **籤詩資料庫**   | 各大宮廟籤詩內容與通用對應釋義          |
| **通用解釋**     | 指引語、啟示、心靈建議等抽象層級說明    |

---

### 2. 🎴 抽牌占卜功能（付費）

網站支援多種互動式抽牌占卜操作：

- **塔羅牌**
- **盧恩符文**
- **六爻**
- **梅花易數**
- **周易本卦**
- **單宮奇門**
- **擲筊占卜**
- **籤詩抽籤**

> 📌 各系統的抽牌邏輯與牌組內容將於內部個別設計與定義，本文不詳述。

#### 抽牌支援模式：

- 快速抽牌
- 編號背面攤牌選擇（遠端互動用）
- 正面全展開選擇（實體同步操作用）

---

### 3. 🎲 擲筊系統（模擬真實機率）

採用 4 張卡牌模擬擲筊，符合實際出現比例：

| 筊象     | 張數 | 說明                     |
|----------|------|--------------------------|
| 聖筊     | 2 張 | 一正一反（允筊）≈ 50%     |
| 笑筊     | 1 張 | 兩正（陰筊）≈ 25%         |
| 怒筊     | 1 張 | 兩反（怒筊）≈ 25%         |

---

## 四、技術架構與開發方式

| 技術模組              | 說明                                     |
|-----------------------|------------------------------------------|
| **Flutter Web**        | 跨平台 UI 開發，支援動畫與卡牌互動操作     |
| **Firebase Hosting**   | 靜態網站部署，支援 CDN 全域加速           |
| **Firebase Auth**      | 多平台使用者登入驗證                     |
| **Firebase Storage**   | 儲存卡牌資料、影片資源與使用者紀錄        |
| **Firebase Functions** | 處理抽牌邏輯與 LLM 解讀服務整合           |
| **LLM 模型 API**       | 已整合專屬解讀邏輯，非第三方即時生成       |
| **綠界金流整合**        | 處理所有付費占卜功能交易                  |

---

## 五、流量與營運策略

- 不進行 SEO，網站不對搜尋引擎開放
- 透過 **短影片平台（YouTube Shorts、TikTok）引流**
- 影片引導觀眾至網站進行同步抽牌
- 由單人開發與營運，確保靈活與可控性

---

## 六、網站核心價值

> 「**選擇比努力更重要。**」  
> 在混沌中做出清晰決定，是現代人最需要的命理指引。  
> 本平台即是你與自己對話的場域。

---

## 七、開發補充說明

- 所有卡牌資料模組化儲存於 Firestore 或 Firebase Storage  
- 靜態查詢資料以 Markdown 或 JSON 格式管理  
- 使用者登入後可解鎖抽牌功能  
- 語言國際化使用 `i18n` JSON 模組（如 `zh-Hans`, `zh-Hant`, `en`）

---



