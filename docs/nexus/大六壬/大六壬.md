# 大六壬排盤方法與AI解讀模型研究報告

## 摘要

本報告旨在深入探討大六壬這一複雜的中國傳統占卜術，並將其排盤方法程式化，同時分析其常用占卜體類，並提出可供人工智慧（AI）解讀的模型。

大六壬以其龐大的資訊量和精密的邏輯推演著稱，其排盤過程高度結構化，為程式化實現提供了堅實基礎。報告將詳細闡述月將、天地盤、四課、三傳以及神將與十二宮的程式化計算步驟。

針對大六壬的豐富判斷體系，報告將梳理並歸納常見的課體類型及其判斷要點，並探討類神與六親在解讀中的應用。

在AI解讀模型方面，報告將重點討論知識圖譜作為核心知識表示的優勢，以及如何進行特徵工程以適應機器學習模型。同時，將評估大型語言模型（LLMs）在此領域的潛力與局限性，並提出整合多種AI技術的混合模型架構，以期實現更精準、可解釋的智慧占卜系統。

## 引言

### 大六壬概述及其在術數中的獨特性

大六壬作為中國古代「三式」（太乙、奇門、六壬）之首，被譽為「占卜之王」，其核心在於對時空體系的全面性把握，以天地人三才相合，探求自然界與人的關係 1。此術數體系深植於中國古代的天文曆法知識，透過精密的運算、推演與判斷，旨在測度人事，並因其對人事的高度重視，被視為三式之最 1。

大六壬的獨特之處在於其精密的結構，由起心動念的時辰幻化為天地盤、四課、三傳等核心結構 4。這種結構體現了道家「道生一、一生二、二生三、三生萬物」的哲學觀念，其中時間資訊為「道」，天地盤為「一」，四課為「二」（陰陽兩分），三傳為「三」（陰陽交易的結果），最終能觀察萬物，故能「一針見血」地揭示事物發生、發展和結果 3。這種直接且清晰的預測能力，使其有別於《易經》等較為抽象、難以捉摸的術數 3。

與《易經》僅有64卦×6爻=384爻的預測資訊相比，大六壬在演算過程中融合了天文、地理、曆算知識，並運用五行、干支、八卦等模型，形成龐大的思想體系。其課式組合可達近三千萬種（720種基本課式 × 2種陰陽 × 12種十二神 × 12種十二將 × 12種地支正反轉），資訊量極為龐大，遠超其他術數，這也使其學習與掌握難度極高 2。大六壬的這種內在結構化特性和龐大的離散狀態空間，使其在計算機建模和符號AI方法上具有顯著優勢。它不僅僅是解釋規則的集合，更是一個從特定輸入（時間、日期等）生成特定輸出（課式及其組成部分）的複雜、確定性演算法。這種明確的輸入、清晰的規則和可預測的輸出，為程式化實現奠定了堅實的基礎，使其在AI應用方面比其他結構較為鬆散的占卜方法更具潛力。

### 報告目標與結構

本報告旨在將大六壬的排盤與判斷規則進行系統性梳理，使其具備可程式化實現的基礎。報告將探討大六壬的常用占卜體類，為AI模型提供高層次的解釋框架。最終，將提出基於知識圖譜、機器學習和大型語言模型的AI解讀模型，以應對大六壬的複雜性和多樣性。

報告將分為排盤程式化、課體分類判斷、AI解讀模型建構三個主要部分，並在結論中展望未來研究方向。

## 大六壬排盤方法之程式化解析

大六壬的排盤過程高度依賴於精確的曆法計算和嚴謹的規則推演，這使其天然具備程式化的潛力。整個排盤流程可被分解為一系列明確的演算法步驟。

### 月將與天地盤的推演算法

#### 月將的確定

月將是排盤的首要步驟，代表太陽在特定地支宮位的位置。其確定方式並非依循傳統農曆的「月建」（即每月的第一個節氣），而是以二十四節氣中的「中氣」為準 5。例如，正月（寅月）的月將為亥，但它並非從「立春」（節氣）開始啟用，而是從「雨水」（中氣）開始，並持續到二月「春分」（中氣）前一日。春分當天，月將則換為戌，其餘各月依此類推 5。

這種依「中氣」換將的規則，是確保大六壬排盤精確性的關鍵。程式化實現時，這要求系統具備一個精確的二十四節氣時間點數據庫，並能根據占卜時間判斷當前所處的「中氣」，進而確定對應的月將。這與一般曆法中以「節氣」作為月份起始的習慣不同，因此，開發者不能簡單地依賴通用曆法庫，而需要專門處理「中氣」換將的邏輯，這是程式化實現中一個容易被忽視但至關重要的細節。

#### 天地盤的佈局

天地盤是大六壬課式的基礎結構，由地盤和天盤兩層組成。地盤是十二地支的固定本位排列，通常以圓盤或方形陣列表示，代表靜態的、基礎的空間方位 5。

天盤的佈局則是一個動態過程。它將已確定的月將加臨到占卜時辰的地支上，然後天盤的其餘地支順時針（或逆時針，取決於具體規則）依次對應地盤上的地支，保持其相對順序 5。例如，若月將為亥，占卜時辰的地支為丑，則天盤的亥將置於地盤的丑之上。隨後，天盤的子將對應地盤的寅，天盤的丑將對應地盤的卯，以此類推，直到天盤的戌對應地盤的子 5。程式化實現時，這可通過定義地盤為固定數組，然後根據月將和時辰地支計算天盤的起始偏移量，再通過模運算（%12）順序填充天盤來完成。天地盤的這種動態關係也用於初步的判斷，例如，辰加子、寅、辰、午、申、戌為陽，辰加丑、卯、巳、未、酉、亥為陰，可用於初步判斷陰晴 6。

### 四課的建立與推導邏輯

#### 四課的定義與構成

四課是大六壬預測的核心結構之一，它捕捉了事件的初始形態，並確立了主客與彼我之間的關係 1。這四組干支對應關係是從占卜當天的日干和日支（日辰），結合已佈局的天地盤推導而來。

四課的具體構成如下 5：

- **第一課（幹上陽神）：** 代表「陽中之陽」或「太陽」。它是日干所寄地支在地盤上對應的天盤地支。
    
- **第二課（幹上陰神）：** 代表「陽中之陰」或「少陽」。它是第一課上神所臨地盤的地支，其在地盤上對應的天盤地支。
    
- **第三課（支上陽神）：** 代表「陰中之陽」或「少陰」。它是日支在地盤上對應的天盤地支。
    
- **第四課（支上陰神）：** 代表「陰中之陰」或「太陰」。它是第三課上神所臨地盤的地支，其在地盤上對應的天盤地支。
    

#### 四課的程式化生成步驟

程式化生成四課的步驟如下：

1. **輸入：** 日干、日支、以及已建立的天地盤映射（天盤地支對應地盤地支）。
    
2. **預處理：** 定義「十干寄宮」的映射關係。例如，甲寄寅、乙寄辰、丙寄巳、丁寄午、戊寄巳、己寄午、庚寄申、辛寄酉、壬寄亥、癸寄子 8。
    
3. **推導第一課（幹上陽神）：**
    
    - 根據「十干寄宮」映射，找到日干所對應的地支 `EB_DayStem_Jigong`。
        
    - 在天地盤中，查找 `EB_DayStem_Jigong` 在地盤上的位置，其對應的天盤地支即為 `HB_FirstClass`。
        
    - 第一課為 (`HB_FirstClass`, `EB_DayStem_Jigong`)。
        
4. **推導第二課（幹上陰神）：**
    
    - 在天地盤中，查找 `HB_FirstClass` 在地盤上的位置，其對應的天盤地支即為 `HB_SecondClass`。
        
    - 第二課為 (`HB_SecondClass`, `HB_FirstClass`)。
        
5. **推導第三課（支上陽神）：**
    
    - 在天地盤中，查找日支 `DayBranch` 在地盤上的位置，其對應的天盤地支即為 `HB_ThirdClass`。
        
    - 第三課為 (`HB_ThirdClass`, `DayBranch`)。
        
6. **推導第四課（支上陰神）：**
    
    - 在天地盤中，查找 `HB_ThirdClass` 在地盤上的位置，其對應的天盤地支即為 `HB_FourthClass`。
        
    - 第四課為 (`HB_FourthClass`, `HB_ThirdClass`)。
        
7. **輸出：** 由四個（天盤地支，地盤地支）對組成的四課。
    

四課的這種明確、規則化的生成過程，為後續的AI解釋奠定了堅實的數據基礎。這種強大的規則基礎和確定性，使得整個排盤過程非常適合演算法實現。每個步驟都可以被轉化為特定的函數或一系列條件語句和查找操作，確保了整個占卜過程的精確性和可驗證性。

### 三傳的取用規則與演算法

三傳（初傳、中傳、末傳）是大六壬預測的關鍵，被視為事物發生、發展、結局三個階段的集中表現 1。其推導的核心原則是「以克取傳」，「克」在五行學說中代表否定、對立與鬥爭，反映了事物辯證發展的規律 2。

#### 九宗門發用規則詳解

「九宗門」是一套用於確定初傳（發用）的嚴格層次化規則。程式化實現時，必須按照特定的優先順序依次判斷，一旦某條規則的條件滿足，即採用該規則取傳，不再向下判斷。

- **賊克法 (Zei Ke Fa - Theft and Conquest Method):**
    
    - 基本原則是優先尋找四課中的「克」關係。首先取「下賊上」（地盤克天盤）的課為初傳。如果有多個下賊上，則取與日干陰陽屬性相同者發用 2。
        
    - 若四課中沒有「下賊上」的情況，則取「上克下」（天盤克地盤）的課為初傳。若有多個上克下，同樣取與日干陰陽屬性相同者發用 2。
        
    - 程式化實現時，這需要遍歷四課，判斷每課的生克關係，並根據優先順序進行篩選。
        
- **比用課 (Bi Yong Ke - Comparison and Use Method):**
    
    - 當四課中出現兩課「下賊上」時，則取與日干陰陽屬性相同者發用 9。
        
- **知一課 (Zhi Yi Ke - Knowing One Method):**
    
    - 當四課中出現兩課「上克下」時，則取與日干陰陽屬性相同者發用 7。這意味著事件存在兩種選擇，需要判斷其與日干的關聯性。
        
- **涉害課 (She Hai Ke - Involving Harm Method):**
    
    - 此法適用於四課中無「下賊上」，但有兩課或更多「上克下」，且這些克課與日干的陰陽屬性相比後，仍無法唯一確定初傳（即都相同或都不同）的情況。此時，取「逢克多者」發用，即追溯其在地盤上被克最深（移動步數最多）的地支為初傳 5。
        
    - 若涉害深淺相同（即克數相同），則進一步細分：
        
        - **見機格 (Jian Ji Ge - Opportunity Seizing Grid):** 取臨地盤「四孟」（寅、申、巳、亥）者發用 9。
            
        - **察微格 (Cha Wei Ge - Subtle Observation Grid):** 若無四孟，則取臨地盤「四仲」（子、午、卯、酉）者發用 9。
            
        - **綴瑕格 (Zhui Xia Ge - Flaw-Adorning Grid):** 若涉害深淺相同，且孟仲季皆無區分，則陽日取居幹上兩課者發用，陰日取居支上兩課者發用 9。
            
    - 程式化實現時，這要求計算每個克課的「涉害深度」，即從天盤上神逆數到地盤本位所經歷的地支數，並根據孟仲季的優先順序進行判斷。
        
- **遙克課 (Yao Ke Ke - Distant Conquest Method):**
    
    - 當四課中沒有直接的「克」關係時，則尋找「遙克」（天盤神遙克日干或日支，或地盤神遙克天盤神）的關係來取初傳。
        
- **昴星課 (Mao Xing Ke - Pleiades Method):**
    
    - 若四課中無直接的「克」，也無「遙克」，則取昴星（酉）為初傳 2。
        
- **別責課 (Bie Ze Ke - Separate Responsibility Method):**
    
    - 此法用於特殊情況，如五陽日取干合上神作初傳，五陰日以地支三合前辰為初傳 5。
        
- **伏吟課 (Fu Yin Ke - Hidden Groan Class):**
    
    - 當天盤和地盤完全重合（例如子加子，午加午）時，則為伏吟課，象徵停滯、困難、不動或被困 2。初傳取有克者；若無克，則取日干所寄地支之刑 2。
        
- **反吟課 (Fan Yin Ke - Reversed Groan Class):**
    
    - 當天盤與地盤對沖（例如子加午，卯加酉）時，則為反吟課，象徵變動、衝散或解除禁錮 2。初傳取沖克者；若無克，則取驛馬 2。
        
- **無依課 (Wu Yi Ke - No Reliance Method):**
    
    - 若以上所有方法都無法取傳，則為無依課，表示事情尚未形成或無法判斷 7。
        

三傳的推導，特別是初傳的確定，是一個複雜的決策樹過程，涉及多層次的條件判斷和優先順序。程式化實現時，這要求建立一個精確且有序的條件邏輯結構，如巢狀的 `if-else if-else` 語句或規則引擎。每條「九宗門」規則都可以被轉化為一個特定的函數或一系列條件判斷。這種複雜性，雖然增加了程式設計的難度，但正是大六壬能夠進行細緻分析的原因，它捕捉了許多微妙的互動關係。這也表明，這些規則可以被表示為程式碼中一系列優先執行的函數或方法。

#### 中末傳的遞推機制

一旦初傳確定，中傳和末傳的推導則相對簡單且具確定性 5。中傳為初傳所臨地盤之神（即初傳天盤地支在地盤上的位置，其對應的天盤地支） 5。末傳則為中傳所臨地盤之神（即中傳天盤地支在地盤上的位置，其對應的天盤地支） 5。這可以簡潔地概括為：「初傳本位明中次，中上因加是末居」 5。程式化實現時，這僅需簡單的查表或映射操作即可完成。

#### 特殊課體的三傳取法

除了上述九宗門的通用規則外，某些特殊課體（如伏吟課、反吟課）具有其獨特的中末傳推導規則，這些特殊規則必須在九宗門之前進行判斷和應用 2。例如，伏吟課在有克時取克，中傳取支上，末傳取刑；若中傳自刑，則末傳取沖 2。這種例外處理機制，要求程式化實現中包含明確的特殊課體檢查，並在條件滿足時優先應用其特定規則。這強調了在計算模型中，全面定義規則和嚴格排序規則的重要性，以確保處理所有可能的盤面配置並維持準確性。

### 神將與十二宮的安配

#### 十二神將的排列與屬性

十二神將（又稱十二天官）是大六壬盤面上的重要元素，它們佈於天盤之上，代表不同的天體影響和象徵意義 11。十二神將的順序是固定的：天乙貴人居中，前方有螣蛇、朱雀、六合、勾陳、青龍，後方有天后、太陰、玄武、太常、白虎，天空則為貴人對沖之位 11。

神將的安配始於天乙貴人，其在天盤上的起始地支由日干以及占卜的晝夜（晝占或夜占）決定 7。其口訣為：「甲戊庚牛羊，乙己鼠猴鄉，丙丁豬雞位，壬癸蛇兔藏，六辛逢馬虎，此是貴人方」 12。口訣中前一個字代表晝占的起始地支，後一個字代表夜占的起始地支，且決定後續神將的順逆行方向 12。程式化實現時，這需要建立日干與貴人起始地支的映射表，判斷晝夜（通常以日出日落時間或固定時辰為界），然後根據順逆行規則在天地盤上安配十二神將。

每個神將都攜帶特定的象徵屬性，這些屬性會因其所臨的宮位（地支）而進一步細化 10。例如，朱雀與文書、火光、口舌、官司相關，但朱雀臨寅則名為「安巢」，主文書遲滯或口舌平息；朱雀臨巳則名為「晝翔」，占口舌詞訟則凶，占文書音信則吉 11。白虎則與災禍、爭鬥、疾病、錢財相關 10。這種多層次的符號系統，其中每個元素都具有內在屬性，且其意義會隨其位置和與其他元素的互動而變化，要求程式化實現中具備複雜的數據結構來表示這些多層次的關係。物件導向程式設計（OOP）可以將每個元素建模為帶有屬性（如陰陽、五行）和方法（如生、克、刑、沖、合、害）的物件，而元素之間的互動則可以表示為關係或函數。這種複雜性雖然是挑戰，但也為AI模型提供了豐富且結構化的特徵，實現高度細緻的分析。

#### 十二宮位的意義與應用

十二宮位，即地盤上的十二地支本位，是承載天盤地支和神將的固定框架 6。每個宮位都與特定的生活面向和象徵意義相關聯，構成了大六壬解釋的基礎 16。

在某些系統中，每個宮位可以同時具有「動態」（應事宮，與事件相關）和「靜態」（本命關係宮，與固有生命面向相關）的意義 17。例如，大安在動態上是事業宮，靜態上則是命宮；留連在動態上是田宅宮，靜態上是奴僕宮 17。大六壬解釋的核心在於天盤地支、神將與其所臨地盤宮位之間的互動 6。神將的屬性會受到其所在宮位的影響，反之亦然。

此外，在排盤過程中還需確定「人元」、「將干」和「神干」。人元由日干遁到地分上得來；將干和神干的定法與人元相同，皆採用「五子元遁法」 7。這些遁干方法是固定的查表或循環計算，可直接編碼實現。

**表1: 大六壬排盤核心步驟與程式化對應**

|步驟名稱|輸入數據|核心規則/演算法|輸出組件|程式化對應（簡要說明）|
|---|---|---|---|---|
|**月將確定**|占卜日期時間（年、月、日、時辰）|精確二十四節氣（中氣）時間點判斷；月將與中氣對應表 (正月亥，二月戌等) 5|確定月將（地支）|`get_month_general(datetime_obj)`：查詢中氣表，返回地支索引|
|**天地盤佈局**|月將、占時地支|地盤固定排列；天盤月將加占時地支，順時針旋轉對應 5|天地盤映射（天盤地支對應地盤地支）|`layout_heaven_earth_plate(month_general, hour_branch)`：地盤數組，天盤旋轉函數|
|**四課建立**|日干、日支、天地盤映射|日干寄宮（甲寅、乙辰等）；四課定義（幹上陽神、幹上陰神、支上陽神、支上陰神）5|四課（四對天盤地支與地盤地支）|`derive_four_classes(day_stem, day_branch, heaven_earth_map)`：查詢寄宮，查找天盤對應|
|**三傳發用**|四課、日干、五行生克關係、晝夜|九宗門規則（賊克、比用、知一、涉害、遙克、昴星、別責、伏吟、反吟、無依）2；涉害深度計算；孟仲季判斷；日干陰陽比對|初傳（地支）|`get_initial_transmission(four_classes, day_stem, is_day_time)`：複雜條件判斷樹，優先級處理|
|**中末傳遞推**|初傳、天地盤映射|中傳為初傳所臨地盤之神；末傳為中傳所臨地盤之神 5|中傳、末傳（地支）|`get_middle_final_transmission(initial_transmission, heaven_earth_map)`：簡單查找|
|**神將安配**|日干、晝夜、天地盤映射|十二神將固定順序；貴人晝夜起法口訣（甲戊庚牛羊等）7；順逆行方向|十二神將在天盤上的位置映射|`place_deities(day_stem, is_day_time, heaven_earth_map)`：貴人起點計算，順逆填充|
|**人元/將干/神干確定**|日干、地支、神將|五子元遁法口訣（甲己還是甲等）7|人元（天干）、將干（天干）、神干（天干）|`derive_stems(day_stem, earthly_branch, deity_branch)`：遁干函數|

## 大六壬常用占卜課體分類與判斷

大六壬的判斷體系極為豐富，其中「課體」作為對盤面結構的高度概括，是判斷吉凶的重要依據。大六壬約有64種基本課體，每種課體都有其特定的構成條件和象義 9。

### 主要課體類型及其特徵

- **元首課 (Prime Minister Class):** 此課體形成條件為四課中僅有一課「上克下」（天盤克地盤），其餘課中無克。取該「上克下」的上神發用。元首課象徵天地得位、尊卑有序，為諸課之首，預示大吉大利，主君臣和合、婚姻和諧、謀為順利、官職晉升等 9。
    
- **重審課 (Re-examination Class):** 當四課中有一課「下賊上」（地盤克天盤），且可能還有其他「上克下」的關係時，取該「下賊上」的上神發用。此課象徵以下犯上，需仔細審視，預示雖有承天載厚之象，但亦有憂驚，事宜後動 9。
    
    - **始入課 (Initial Entry Class):** 這是重審課的一個特例，指四課中只有一課「下賊上」，而無「上克下」的情況。它象徵新事物的開始 9。
        
- **知一課 (Knowing One Class):** 形成於四課中有兩課「上克下」時，此時取與日干陰陽屬性相比者發用。此課象徵事情分為兩端，需要做出選擇，災禍可能來自外部 9。
    
- **比用課 (Comparison and Use Class):** 形成於四課中有兩課「下賊上」時，取與日干陰陽屬性相比者發用。同樣預示事分兩端，需選擇其中之一 9。
    
- **涉害課 (Involving Harm Class):** 此課體適用於四課中有多個「克」（無論上克下或下賊上），且經過日干陰陽比對後仍無法唯一確定初傳的情況。此時，取「逢克最多者」發用，即追溯其在地盤上被克最深的地支。涉害課預示凡事艱難，多費苦心方能成就，災難消解較遲 9。
    
    - **見機格 (Opportunity Seizing Grid):** 若涉害深淺相同，則取臨地盤「四孟」（寅、申、巳、亥）者發用。此格預示宜見機而行，遲疑則失 9。
        
    - **察微格 (Subtle Observation Grid):** 若涉害深淺相同，且無四孟，則取臨地盤「四仲」（子、午、卯、酉）者發用。此格預示人情淺薄，小人謀害，需謹防不仁 9。
        
    - **綴瑕格 (Flaw-Adorning Grid):** 若涉害深淺相同，且孟仲季皆無區分，則陽日取居幹上兩課者發用，陰日取居支上兩課者發用。此格預示兩雄相爭持久，牽連眾多，災耗不絕 9。
        
- **伏吟課 (Hidden Groan Class):** 天盤與地盤完全重合（例如子加子，午加午）。此課象徵事物靜止、難受、困滯或被拘捕 2。
    
- **反吟課 (Reversed Groan Class):** 天盤與地盤對沖（例如子加午，卯加酉）。此課象徵事物動盪、變動、衝散，好事可能辦不成，壞事可能不成凶 2。
    

程式化實現時，每個課體都可以被定義為一個函數或方法，接收四課和三傳作為輸入，然後根據其特定的判斷條件返回課體名稱及其基本象義。

### 課體判斷的綜合原則

大六壬的判斷並非單一地依據課體，而是一個高度綜合性的過程。它將課體、類神、六親、年命、天地盤、四課、三傳、旺相休囚、長生十二位、貴人順逆等多方面資訊納入考量 18。這種判斷邏輯的層次性和交叉影響，是其預測精準的關鍵。

- **課體與傳的關係：** 課體是判斷的主導因素，但其「實質」在於課傳組合，囊括了天地盤、四課、三傳、年命等所有資訊 18。四課通常代表事物的現狀，而三傳則揭示了事情的發生、發展和最終結果 5。例如，若三傳出現，則事情已然發生或即將發生；若僅在四課出現而未上三傳，則說明事情正在醞釀中；若四課也未出現，則事物尚未形成 7。
    
- **五行「氣」的旺衰：** 「旺相休囚」和「長生十二位」（長生、沐浴、冠帶、臨官、帝旺、衰、病、死、墓、絕、胎、養）是判斷五行「氣」的強弱和事物發展趨勢的重要依據 2。這些狀態反映了能量的聚集與耗散，決定了事物的成敗。例如，地盤上日干死墓，而天盤長生臨官，則為進氣，反之為退氣 2。
    
- **貴人順逆：** 貴人的順行和逆行，代表事物運動的快慢和順逆，直接影響吉凶的應期 7。貴人順行則事物順利，逆行則受阻。
    
- **四課的完整性：** 四課是否齊全（四課全、三課、兩課）也影響判斷。例如，四課全則氣數充足，預示事物發展有力；若出現三課或兩課，則可能預示事物不足月或存在缺陷，例如孕產可能早產或難產，生意可能虧損 7。
    
- **「克」的意義：** 「克」是事物轉折的關鍵。四課無克，說明事物的「機」（時機）尚未成熟，不宜行動；只要有克，則說明事物將要發生變化，或主動方將採取行動，此時即為「發用」，代表「動」 7。
    

這種多層次的判斷邏輯，對於AI模型而言，意味著單純依賴課體分類的模型將會不足。需要更為複雜的模型，能夠整合來自不同層次的特徵（例如多輸入神經網絡、貝葉斯網絡，或帶權重的規則系統）。其挑戰在於量化這些交互因素的「核定」過程和相對重要性，這往往依賴於人類專家的直覺。這也正是機器學習可能發揮作用的地方，它可以從大量的歷史案例中發現人類專家難以明確表達的微妙模式。

### 類神與六親的應用

「類神」是大六壬判斷中不可或缺的輔助工具，它將課傳中的地支、神將等元素與現實中的具體人、事、物進行對應 13。每一個神將都可能有多種甚至上百種判斷方法，幫助預測者迅速準確地找到與所問事務相關的資訊 14。

例如：

- **朱雀：** 為文書、火光、血光、口舌、訴訟、音信等 11。
    
- **青龍：** 為官職、財富、婚姻中的夫方、吉祥之事 11。
    
- **天后：** 為婦人、母親、婚姻中的婦方 11。
    
- **白虎：** 為災禍、爭鬥、官司、傷害、疾病、錢財 10。
    
- **玄武：** 為盜賊、暗昧之事、虛詐、水 11。
    

「六親」關係則是以日干為主體，將五行生克關係人格化，使其更為通俗易懂 2。例如，克日干者為官鬼（代表官非、疾病、丈夫），生日干者為父母（代表長輩、學業、文書），日干所克者為妻財（代表妻子、錢財），日干所生者為子孫（代表子女、福德），與日干比和者為兄弟（代表兄弟、朋友） 2。

類神和六親的應用具有強烈的上下文相關性。例如，在占卜婚姻時，男方占卜需看財爻和神後，而女方占卜則需看官爻和貴人 13。程式化實現時，這要求建立一個龐大的類神知識庫，將每個神將、地支、五行等元素與其在不同情境下的類象進行多對多映射。六親關係則可通過計算日干與其他元素的五行生克關係來確定。

**表2: 大六壬常用課體類型與判斷要點**

|課體名稱|形成條件（簡述）|基本象義/判斷要點|程式化判斷邏輯（簡要說明）|
|---|---|---|---|
|**元首課**|四課中僅一課上克下，其餘無克 9|天地得位，尊卑有序，大吉大利；君臣和合，謀為順利 9|`if (count_上克下 == 1 and count_下賊上 == 0)`|
|**重審課**|四課中有一課下賊上，其餘課有或無上克下 9|以下犯上，需詳審；雖有承天載厚，但有憂驚，事宜後動 9|`if (count_下賊上 >= 1)`|
|**始入課**|重審課特例：僅有一課下賊上，無上克下 9|新事物的開始，有憂驚，事宜後動 9|`if (count_下賊上 == 1 and count_上克下 == 0)`|
|**知一課**|四課中有兩課上克下，取與日干陰陽相比者發用 9|事分兩端，需做出選擇；事情起於同類，災禍自外來 9|`if (count_上克下 == 2 and compare_yin_yang_match)`|
|**比用課**|四課中有兩課下賊上，取與日干陰陽相比者發用 9|事分兩端，需選擇陰陽相比者；凡事狐疑不決 9|`if (count_下賊上 == 2 and compare_yin_yang_match)`|
|**涉害課**|四課中多個克，取逢克最多者發用；或涉害深淺相同則按孟仲季、干支上兩課取 5|凡事艱難，多費苦心方成；婚姻有阻，疾病難安 9|`if (multiple_clashes_unresolved_by_yin_yang)` then `calculate_shehai_depth` and apply sub-rules|
|**伏吟課**|天盤與地盤重合（子加子等）2|難受，不動，困滯，被拘捕 2|`if (heaven_plate_matches_earth_plate)`|
|**反吟課**|天盤與地盤對沖（子加午等）2|身動，變動，衝散，禁錮解除 2|`if (heaven_plate_is_opposite_earth_plate)`|
|**遙克課**|四課無克，但有遙克關係|事物有遠處影響，或隱性矛盾|`if (no_direct_clash and has_remote_clash)`|
|**昴星課**|四課無克，無遙克 2|有人在虎視眈眈，潛在危險 2|`if (no_clash and no_remote_clash)` then `initial_transmission = '酉'`|
|**別責課**|特定日干（五陽日）或地支（五陰日）的特殊取法 5|應對特殊情境，確保取傳|`if (special_day_stem_or_branch_conditions_met)`|

## 大六壬AI解讀模型建構

將大六壬的複雜知識體系轉化為AI可理解和處理的模型，需要精密的知識表示、特徵工程以及多種AI技術的整合。

### 知識表示與特徵工程

#### 大六壬數據的結構化

要使AI能夠解讀大六壬，首先必須將盤面上的所有元素和其相互關係轉化為機器可讀的結構化數據。這包括天干、地支、月將、神將、四課、三傳、年命、六親、神煞、旺相休囚、十二長生狀態等所有構成占卜結果的要素 1。這些數據可以採用JSON、XML等半結構化格式，或存儲於關係型數據庫中。例如，每個地支宮位可以表示為一個節點，其上的天盤地支、神將、六親關係等則作為該節點的屬性。

#### 關鍵特徵的提取與轉換

將大六壬的符號化和定性信息轉化為數值特徵，是應用機器學習的關鍵步驟 19。

- **數值化處理：** 天干、地支、五行、陰陽、神將等符號信息可以通過獨熱編碼（One-Hot Encoding）轉化為二元向量，或通過標籤編碼（Label Encoding）轉化為整數。例如，將「木」映射為 ``，將「陽」映射為 `1`。對於「旺相休囚」和「十二長生狀態」等定性狀態，可以將其映射為數值強度指標（例如，旺=4，相=3，休=2，囚=1，死=0；長生=11，沐浴=10...絕=0），以捕捉其強弱趨勢 2。
    
- **交互特徵（Feature Crosses）：** 大六壬的判斷高度依賴於元素間的相互作用（如生克、刑沖合害、加臨、乘），因此構建交互特徵至關重要 19。例如，可以創建一個二元特徵來表示「天盤X加臨地盤Y」，或表示「神將A克制六親B」。這些交互特徵能夠捕捉傳統解釋中複雜的組合意義。
    
- **時間序列特徵：** 三傳代表事物發展的三個階段，可以將其視為一個短序列，提取其趨勢、變化率等序列特徵，或使用序列模型進行處理。
    
- **高維特徵降維：** 由於大六壬的課式組合數量龐大（近三千萬種），經過特徵工程後可能產生高維特徵空間。此時，可以採用主成分分析（PCA）或線性判別分析（LDA）等降維方法，減少冗餘信息，提高模型效率和泛化能力 20。
    

將傳統的符號化知識轉化為機器學習可處理的數值特徵，需要深入的領域知識。這不僅僅是數據格式的轉換，更是將玄學概念映射到數學空間的過程，確保數值特徵能夠準確地代表其在傳統體系中的意義和相互關係。

### 基於知識圖譜的推理模型

大六壬的知識體系具有高度的結構化和關係性，這使其非常適合採用知識圖譜（Knowledge Graph, KG）進行知識表示和推理 23。

#### 知識圖譜的構建方法

知識圖譜的構建可以將大六壬的各類知識（實體、關係、屬性）進行系統化整理：

- **實體（Entities）：** 包括天干（甲、乙、丙、丁、戊、己、庚、辛、壬、癸）、地支（子、丑、寅、卯、辰、巳、午、未、申、酉、戌、亥）、月將（亥、戌、酉等）、十二神將（天乙貴人、騰蛇、朱雀、六合、勾陳、青龍、天空、白虎、太常、玄武、太陰、天后）7、課體（元首課、重審課、涉害課等）9、六親（官鬼、父母、妻財、子孫、兄弟）2、神煞（驛馬、空亡、月厭等）10 等。
    
- **關係（Relations）：** 包括五行生克關係（例如「木生火」、「金克木」）、位置關係（例如「天盤X加臨地盤Y」、「神將A乘地支B」）、推導關係（例如「初傳來自某課」、「某神將為某事類神」）、刑沖合害關係（例如「子午相沖」、「寅巳相刑」）8。
    

知識圖譜的構建流程可以遵循「四步法」：首先進行領域本體構建，定義核心概念和關係；其次通過眾包或半自動化方式進行語義標註，從文本中抽取知識；然後補全外源數據；最後進行信息抽取，擴充知識庫 24。知識圖譜作為大六壬「專家」知識的結構化載體，能夠清晰地表示其龐大而複雜的知識體系。它不僅可以存儲固定關係（如五行循環、神將順序），也能表示動態關係（如特定神將在不同宮位的解釋、課體與四課結構的關聯）。這使得AI能夠像人類專家一樣，通過「遍歷」圖譜來推斷新的事實或解釋，實現符號推理，並天然地支持解釋性，因為推理路徑可以在圖譜中被追蹤。

#### 推理機制與應用場景

基於知識圖譜的推理模型可以實現多種推理機制：

- **基於規則的推理：** 將大六壬的判斷規則（如九宗門、畢法賦條文、類神判斷）編碼為邏輯規則，利用推理機進行正向推理（從已知條件推導結論）、反向推理（從假設結論尋找支持證據）或雙向推理 26。
    
- **知識推理：** 利用知識圖譜的結構，發現隱含關係。例如，如果已知「木克土」且「土克水」，則可以推理出「木間接克水」的關係 25。
    
- **應用場景：** 知識圖譜在AI解讀大六壬方面具有廣泛應用。它可以實現智慧排盤、自動識別課體、進行吉凶判斷、預測事件發展趨勢，並針對特定問題（如婚姻、財運、健康）提供專項解讀。此外，其透明性使得AI能夠清晰地呈現推理路徑和判斷依據，這對於占卜領域的用戶信任至關重要 26。
    

### 機器學習與深度學習的潛力

機器學習和深度學習技術在大六壬的解釋中具有潛力，尤其是在處理複雜模式識別和從數據中學習隱藏規律方面。

#### 模型訓練與預測

- **監督學習：** 可以將大量的歷史占卜案例作為訓練數據。輸入是排盤後結構化的數據（如表3所示的特徵），輸出是人類專家對該課式的判斷結果（例如吉凶分類、事件趨勢預測）。
    
- **模型選擇：** 傳統機器學習模型如決策樹、支持向量機（SVM）、隨機森林等，適用於處理結構化特徵。對於序列信息（如三傳的演變），循環神經網絡（RNN）或變換器（Transformer）等深度學習模型可能更為合適 30。
    
- **潛力：** 機器學習模型能夠從大量數據中學習複雜的非線性關係，可能發現人類專家難以察覺的微妙模式，從而提高預測的準確性。
    

#### 挑戰與局限性

儘管潛力巨大，但將機器學習和深度學習應用於大六壬也面臨顯著挑戰：

- **數據稀疏性與質量：** 大六壬的課式組合數量龐大（近三千萬種），這導致高質量、標準化的歷史案例數據相對稀缺。更為嚴峻的是，傳統文獻中存在大量的錯誤和歧義（例如超過一千個錯處，包括晝夜貴人顛倒、錯字、漏字、贅字等），這需要大量的人工知識工程來校訂和標準化數據 2。若直接使用未經校訂的數據，將導致AI模型學習到錯誤的規律。
    
- **解釋性與透明度：** 機器學習模型，特別是深度學習模型，通常被認為是「黑箱」，難以解釋其判斷依據。這與占卜領域對「為何如此」的追問相悖 26。用戶可能不僅需要一個預測結果，更需要理解其背後的推導邏輯。
    
- **上下文理解與靈活性：** 大六壬的判斷高度依賴於上下文和靈活的「類神」應用，這對純粹的統計模型構成挑戰。大型語言模型（LLMs）雖然在自然語言理解和生成方面表現出色，但其「一本正經胡說八道」（Hallucination）的事實謬誤問題使其不適合直接用於核心的判斷邏輯 23。LLMs可能生成看似合理的解釋，但其內容可能與大六壬的實際規則不符，這會嚴重損害系統的可靠性。
    

將AI應用於玄學領域，不僅是技術挑戰，也涉及倫理與實用性考量。AI模型若未能提供透明且準確的解釋，可能導致用戶信任度降低。因此，一個僅僅基於數據驅動的黑箱模型，即使預測準確，也可能在實際應用中因缺乏解釋性而受限。這使得「專家系統」概念中強調的「透明性」和「解釋機制」變得至關重要 26。

**表3: 大六壬核心要素與AI特徵映射**

|大六壬核心要素|AI特徵類型|具體映射示例|
|---|---|---|
|**天干** (日干、時干)|獨熱編碼 (One-Hot Encoding)|甲=[1,0,...,0]，乙=[0,1,...,0]|
|**地支** (日支、時支、月將、天地盤十二宮位)|獨熱編碼|子=[1,0,...,0]，丑=[0,1,...,0]|
|**五行屬性** (木、火、土、金、水)|獨熱編碼或數值映射|木=，或 1|
|**陰陽屬性** (陽、陰)|數值映射|陽=1，陰=0|
|**四課** (上神、陰神)|關係編碼 (Relational Encoding) / 獨熱編碼|`(干上陽神_天盤, 干上陽神_地盤)` 的組合獨熱編碼；或知識圖譜中的節點與關係|
|**三傳** (初傳、中傳、末傳)|獨熱編碼 / 序列特徵 / 關係編碼|每個傳的獨熱編碼；或 `(初傳, 中傳, 末傳)` 序列；或知識圖譜中的推導關係|
|**十二神將** (天乙、騰蛇等)|獨熱編碼|天乙=[1,0,...,0]|
|**六親** (官鬼、父母、妻財等)|獨熱編碼|官鬼=[1,0,...,0]|
|**旺相休囚** (旺、相、休、囚、死)|數值強度映射|旺=4，相=3，休=2，囚=1，死=0|
|**十二長生** (長生、沐浴等)|數值強度映射|長生=11，沐浴=10，...，絕=0|
|**神煞** (驛馬、空亡、月厭等)|獨熱編碼|驛馬=[1,0,...,0]|
|**課體類型** (元首課、涉害課等)|獨熱編碼 / 分類標籤|元首課=[1,0,...,0]；或作為分類模型的輸出類別|
|**交互特徵** (例如：天盤X加臨地盤Y，神將A克六親B)|特徵組合 (Feature Crosses)|`(天盤X_獨熱編碼, 地盤Y_獨熱編碼)` 的笛卡爾積；或知識圖譜中的邊屬性|

## 結論與展望

### 總結程式化與AI應用的可行性

大六壬作為一個高度結構化、規則明確的占卜體系，其排盤方法完全可程式化實現。從月將的確定、天地盤的佈局、四課的建立，到三傳的推導，每個步驟都基於清晰的演算法和口訣，為AI應用奠定了堅實的數據和邏輯基礎。儘管其判斷邏輯複雜且多層次，涉及課體、類神、六親、神將、旺相休囚等多維度因素的綜合考量，但其內在的「邏輯辯證及推理」特性使其非常適合基於知識的AI方法，如專家系統和知識圖譜。

AI在處理大六壬龐大的課式組合（近三千萬種潛在課式）和複雜判斷規則方面具有顯著優勢，能夠顯著提高排盤和初步判斷的效率和一致性，克服人類專家在處理巨量資訊時可能遇到的局限。

### 未來研究方向與建議

為實現一個精準、可解釋的大六壬AI解讀系統，未來研究應聚焦於以下幾個方面：

- **知識工程與數據標準化：** 這是AI模型成功的基石。當前傳統文獻中存在大量錯誤和歧義，因此必須優先投入資源進行大規模的知識工程，由資深專家對傳統規則和解釋進行校訂、標準化和數位化。這將建立一個高質量、無歧義的知識庫，並為機器學習模型提供可靠的帶標籤歷史案例數據集。
    
- **混合AI模型研發：** 鑒於大六壬的複雜性和對解釋性的需求，建議採用混合AI架構。
    
    - **知識圖譜**應作為核心知識表示層，用於存儲和管理大六壬的實體（如天干、地支、神將、課體）及其複雜的關係（生克、加臨、類神等）。這將使得AI能夠進行符號推理，並提供透明的解釋路徑。
        
    - **規則引擎**可直接編碼九宗門等確定性強、邏輯清晰的排盤和判斷規則，確保核心計算的準確性。
        
    - **機器學習/深度學習**模型可在知識圖譜和規則引擎的基礎上，用於處理更為複雜、模式化或難以明確規則化的判斷情境，例如從大量案例中學習微妙的吉凶傾向，或對多種因素的綜合影響進行權重評估。
        
- **可解釋AI (XAI) 的實踐：** 在模型設計中，應高度重視AI模型的解釋性。開發能夠清晰呈現推理路徑和判斷依據的模塊，例如透過知識圖譜的可視化，或將推理步驟轉化為自然語言解釋。這將增強用戶對AI判斷的信任和理解，滿足占卜領域對「為何如此」的深層次追問。
    
- **自然語言界面與生成：** 利用大型語言模型（LLMs）強大的自然語言理解和生成能力，將AI的結構化判斷結果轉化為自然、流暢、易懂的占卜解釋和諮詢對話。然而，必須確保LLM的輸出嚴格基於核心知識圖譜和規則引擎的判斷，避免其「一本正經胡說八道」的事實謬誤問題，以維持系統的可靠性。
    
- **應用場景拓展：** 除了基礎的占卜預測，大六壬AI系統還可探索在輔助教學（例如互動式排盤練習、規則解析）、歷史案例分析、學術研究（例如驗證古籍規則、發現新模式）等方面的應用，從而促進傳統文化的現代化傳承與發展。
    
- **倫理與社會影響：** 在開發和推廣大六壬AI系統時，應審慎考慮其倫理邊界。避免過度宣傳或誤導用戶，確保技術的負責任使用，將AI定位為一個強大的分析和輔助工具，而非一個絕對 infallible 的預測者。