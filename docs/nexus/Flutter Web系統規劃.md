# AI 命理服務平台系統規劃與 RAG 優化方案

### 一、系統架構概述

本系統旨在建立一個安全、高效且可擴展的 AI 命理服務平台。整個架構採用**事件驅動**的模式，將前端使用者介面與後端核心處理邏輯解耦，確保系統的穩定性與未來擴展性。

**核心目標：**

- **安全性**：保護 AI 模型與 API 金鑰，不讓其暴露於客戶端。
    
- **成本效益**：利用本地硬體執行 LLM，減少雲端運算費用。
    
- **專業性**：透過 RAG 技術，讓 AI 的回覆更精準且具備專業口吻。
    
- **模組化**：方便為不同命理項目新增獨立的知識庫。
    

### 二、技術選型與工作流程

|   |   |   |
|---|---|---|
|**類別**|**建議技術**|**功能說明**|
|**前端 (客戶端)**|**Flutter Web**|統一的程式碼庫，可輕鬆部署於網頁、桌面與行動裝置。與 **Firestore** 直接溝通，傳送與接收請求。|
|**後端 (雲端服務)**|**Firebase (Firestore)**|作為**中央數據交換中心**。前端寫入新請求後，後端服務端持續監聽文件變動。|
|**後端 (服務端)**|**Mac mini M2 (24G)**|強大的本地運算硬體，執行 **n8n**、**LM Studio** 與 **AnythingLLM**。|
|**工作流自動化**|**n8n**|作為**自動化工作流引擎**，負責監聽 Firestore 的文件變動，並協調觸發後續的 AI 處理任務。|
|**LLM 服務**|**LM Studio**|提供**本地 LLM API 服務**。你可以在此下載並運行開源模型（例如 Llama 3），並以標準 API 格式提供給 AnythingLLM 呼叫。|
|**RAG 系統**|**AnythingLLM**|**一體化的 RAG 解決方案**，提供視覺化介面管理知識庫（你的命理文件）、執行向量化與檢索，並與本地 LLM API 無縫整合。|

**工作流程：**

1. 使用者在 **Flutter Web** 應用中提交命理請求。
    
2. 請求被寫入 **Firestore** 的一個新文件，狀態（`status`）為 `pending`。
    
3. **n8n** 偵測到 Firestore 文件的 `pending` 狀態，觸發工作流。
    
4. **n8n** 工作流呼叫 **AnythingLLM** 的 API，傳送使用者的請求。
    
5. **AnythingLLM** 在本地執行 RAG 流程，從知識庫檢索相關資訊，並將請求與檢索結果發送給 **LM Studio** 的本地 LLM API。
    
6. **LM Studio** 的 LLM 模型生成專業解讀。
    
7. **AnythingLLM** 接收到 LLM 的回覆，並將結果返回給 **n8n**。
    
8. **n8n** 將最終解讀寫回 **Firestore** 的原始文件，並將 `status` 更新為 `completed`。
    
9. **Flutter Web** 偵測到文件狀態變為 `completed`，顯示最終結果給使用者。
    

### 三、RAG 回答優化方案：避免「翻書」式解讀

僅僅使用 RAG 很容易讓 AI 的回答顯得生硬且缺乏連貫性。為了解決這個問題，我們將在 RAG 流程中加入**多階段處理**與**優化提示詞**的技術，讓 AI 的回覆更像一位專業的命理大師。

1. **賦予 AI 專業角色**
    
    - 在傳送給 LLM 的提示詞中，明確給予它一個角色。
        
    - **範例**：`你現在是一位經驗豐富的命理大師，精通塔羅、占星、八字等學問。你的口吻應專業、溫和且具備同理心。`
        
2. **多階段的提示詞工程**
    
    - **階段一：要求 AI 整理資訊（資訊整理）**：讓 LLM 不僅是接收檢索到的資訊，還要先進行內部整理。
        
    - **提示詞**：`以下是關於這個問題的相關命理資料。請你將這些資料融會貫通，提取出核心觀點與關鍵詞。`
        
    - **階段二：要求 AI 創作解讀（風格創作）**：將整理後的關鍵點和原始問題再次送給 LLM，這次要求它用「專業口吻」來生成完整的解讀。
        
    - **提示詞**：`根據你剛剛整理的關鍵點，並結合你的專業知識，請以一位命理大師的角度，為客戶撰寫一個連貫、有深度且易於理解的解讀。請避免直接複製原始資料的內容。`
        
3. **建立結構化輸出**
    
    - 在提示詞中，可以要求 LLM 以結構化的方式回答，例如：
        
        - `主要分析：...`
            
        - `建議與方向：...`
            
        - `總結：...`
            
    - 這將有助於讓回答更清晰，也避免零散的文本。
        

這些優化方案將確保你的 AI 命理服務不僅能提供準確的資訊，還能以引人入勝且專業的口吻呈現，從而提升使用者體驗。