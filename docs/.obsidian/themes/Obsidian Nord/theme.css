
:root
{
    --dark0_x:  46,52,64; /* #2e3440 */
    --dark0:    rgb(var(--dark0_x));
    --dark1_x:  59,66,82; /* #3b4252 */
    --dark1:    rgb(var(--dark1_x));
    --dark2_x:  67,76,94; /* #434c5e */
    --dark2:    rgb(var(--dark2_x));
    --dark3_x:  76,86,106; /* #4c566a */
    --dark3:    rgb(var(--dark3_x));

    --light0_x: 216,222,233; /* #d8dee9 */
    --light0:   rgb(var(--light0_x));
    --light1_x: 229,233,240; /* #e5e9f0 */
    --light1:   rgb(var(--light1_x));
    --light2_x: 236,239,244; /* #eceff4 */
    --light2:   rgb(var(--light2_x));
    --light3_x: 255,255,255; /* #ffffff */
    --light3:   rgb(var(--light3_x));

    --frost0_x: 143,188,187; /* #8fbcbb */
    --frost0:   rgb(var(--frost0_x));
    --frost1_x: 136,192,208; /* #88c0d0 */
    --frost1:   rgb(var(--frost1_x));
    --frost2_x: 129,161,193; /* #81a1c1 */
    --frost2:   rgb(var(--frost2_x));
    --frost3_x: 94,129,172; /* #5e81ac */
    --frost3:   rgb(var(--frost3_x));

    --red_x:    191,97,106; /* #bf616a */
    --red:      rgb(var(--red_x));
    --orange_x: 208,135,112; /* #d08770 */
    --orange:   rgb(var(--orange_x));
    --yellow_x: 235,203,139; /* #ebcb8b */
    --yellow:   rgb(var(--yellow_x));
    --green_x:  163,190,140; /* #a3be8c */
    --green:    rgb(var(--green_x));
    --purple_x: 180,142,173; /* #b48ead */
    --purple:   rgb(var(--purple_x));
}

body
{
    --accent-h: 354; /* --red #bf616a */
    --accent-s: 42%;
    --accent-l: 56%;

    --link-decoration:                none;
    --link-decoration-hover:          none;
    --link-external-decoration:       none;
    --link-external-decoration-hover: none;

    --tag-decoration:                 none;
    --tag-decoration-hover:           underline;
    --tag-padding-x:                  .5em;
    --tag-padding-y:                  .2em;
    --tag-radius:                     .5em;

    --tab-font-weight:                600;
    --bold-weight:                    600;

    --checkbox-radius:                0;

    /* --list-indent:                    2em; */

    --embed-border-left: 6px double var(--interactive-accent);
}

.theme-dark
{
    --color-red-rgb:                 var(--red_x);
    --color-red:                     var(--red);
    --color-purple-rgb:              var(--purple_x);
    --color-purple:                  var(--purple);
    --color-green-rgb:               var(--green_x);
    --color-green:                   var(--green);
    --color-cyan-rgb:                var(--frost1_x);
    --color-cyan:                    var(--frost1);
    --color-blue-rgb:                var(--frost3_x);
    --color-blue:                    var(--frost3);
    --color-yellow-rgb:              var(--yellow_x);
    --color-yellow:                  var(--yellow);
    --color-orange-rgb:              var(--orange_x);
    --color-orange:                  var(--orange);
    /* --color-pink:                    var(--purple); */

    --background-primary:            var(--dark0);
    --background-primary-alt:        var(--dark0);
    --background-secondary:          var(--dark1);
    --background-secondary-alt:      var(--dark2);
    --background-modifier-border:    var(--dark2);

    --cursor-line-background:        rgba(var(--red_x), 0.2);

    --text-normal:                   var(--light2);
    --text-faint:                    var(--light0);
    --text-muted:                    var(--light1);

    --link-url:                      var(--purple);

    --h1-color:                      var(--red);
    --h2-color:                      var(--yellow);
    --h3-color:                      var(--green);
    --h4-color:                      var(--purple);
    --h5-color:                      var(--frost0);
    --h6-color:                      var(--frost2);

    --text-highlight-bg:             var(--frost1);
    --text-highlight-fg:             var(--dark0);

    --text-accent:                   var(--orange);
    --text-accent-hover:             var(--frost2);

    --tag-color:                     var(--frost0);
    --tag-background:                var(--dark2);
    --tag-background-hover:          var(--dark1);

    --titlebar-text-color-focused:   var(--red);

    --inline-title-color:            var(--yellow);

    --bold-color:                    var(--yellow);
    --italic-color:                  var(--yellow);

    --checkbox-color:                var(--frost0);
    --checkbox-color-hover:          var(--frost0);
    --checkbox-border-color:         var(--frost0);
    --checkbox-border-color-hover:   var(--frost0);
    --checklist-done-color:          rgba(var(--light2_x), 0.5);

    --table-header-background:       hsl(220, 16%, 16%);
    --table-header-background-hover: var(--dark3);
    --table-row-even-background:     hsl(220, 16%, 20%);
    --table-row-odd-background:      hsl(220, 16%, 24%);
    --table-row-background-hover:    var(--dark3);

    --text-selection:                rgba(var(--red_x), 0.6);
    --flashing-background:           rgba(var(--red_x), 0.3);

    --code-normal:                   var(--frost1);
    --code-background:               var(--dark1);

    --mermaid-note:                  var(--frost3);
    --mermaid-loopline:              var(--frost1);
    --mermaid-exclude:               var(--dark3);
    --mermaid-seqnum:                var(--dark0);

    --icon-color-hover:              var(--red);
    --icon-color-focused:            var(--frost2);

    --nav-item-color-hover:          var(--red);
    --nav-item-color-active:         var(--frost2);
    --nav-file-tag:                  rgba(var(--yellow_x), 0.9);

    --graph-line:                    var(--dark3);
    --graph-node:                    var(--light3);
    --graph-node-tag:                var(--red);
    --graph-node-attachment:         var(--green);

    --calendar-hover:                var(--red);
    --calendar-background-hover:     var(--dark3);
    --calendar-week:                 var(--yellow);
    --calendar-today:                var(--yellow);

    --dataview-key:                  var(--text-faint);
    --dataview-key-background:       rgba(var(--frost2_x), 0.3);
    --dataview-value:                var(--text-faint);
    --dataview-value-background:     rgba(var(--red_x), 0.3);

    --tab-text-color-focused-active:         var(--frost2);
    --tab-text-color-focused-active-current: var(--red);
}

.theme-light
{
    --color-red-rgb:                 var(--red_x);
    --color-red:                     var(--red);
    --color-purple-rgb:              var(--purple_x);
    --color-purple:                  var(--purple);
    --color-green-rgb:               var(--green_x);
    --color-green:                   var(--green);
    --color-cyan-rgb:                var(--frost1_x);
    --color-cyan:                    var(--frost1);
    --color-blue-rgb:                var(--frost3_x);
    --color-blue:                    var(--frost3);
    --color-yellow-rgb:              var(--yellow_x);
    --color-yellow:                  var(--yellow);
    --color-orange-rgb:              var(--orange_x);
    --color-orange:                  var(--orange);
    /* --color-pink:                    var(--purple); */

    --background-primary:            var(--light3);
    --background-primary-alt:        var(--light3);
    --background-secondary:          var(--light2);
    --background-secondary-alt:      var(--light1);
    --background-modifier-border:    var(--light1);

    --cursor-line-background:        rgba(var(--red_x), 0.1);

    --text-normal:                   var(--dark2);
    --text-faint:                    var(--dark0);
    --text-muted:                    var(--dark1);

    --link-url:                      var(--purple);

    --h1-color:                      var(--red);
    --h2-color:                      var(--yellow);
    --h3-color:                      var(--green);
    --h4-color:                      var(--purple);
    --h5-color:                      var(--frost0);
    --h6-color:                      var(--frost2);

    --text-highlight-bg:             var(--yellow);
    --text-highlight-fg:             var(--dark0);

    --text-accent:                   var(--orange);
    --text-accent-hover:             var(--frost2);

    --tag-color:                     var(--dark3);
    --tag-background:                var(--light1);
    --tag-background-hover:          var(--light0);

    --titlebar-text-color-focused:   var(--red);

    --inline-title-color:            var(--yellow);

    --bold-color:                    var(--green);
    --italic-color:                  var(--green);

    --checkbox-color:                var(--frost2);
    --checkbox-color-hover:          var(--frost2);
    --checkbox-border-color:         var(--frost2);
    --checkbox-border-color-hover:   var(--frost2);
    --checklist-done-color:          rgba(var(--dark2_x), 0.4);

    --table-header-background:       rgba(var(--light2_x), 0.2);
    --table-header-background-hover: var(--frost2);
    --table-row-even-background:     rgba(var(--light2_x), 0.4);
    --table-row-odd-background:      rgba(var(--light2_x), 0.8);
    --table-row-background-hover:    var(--frost2);

    --text-selection:                rgba(var(--red_x), 0.6);
    --flashing-background:           rgba(var(--red_x), 0.3);

    --code-normal:                   var(--frost1);
    --code-background:               var(--light2);

    --mermaid-note:                  var(--frost0);
    --mermaid-loopline:              var(--frost1);
    --mermaid-exclude:               var(--light0);
    --mermaid-seqnum:                var(--light0);

    --icon-color-hover:              var(--red);
    --icon-color-focused:            var(--frost3);

    --nav-item-color-hover:          var(--red);
    --nav-item-color-active:         var(--frost2);
    --nav-file-tag:                  rgba(var(--orange_x), 0.9);

    --graph-line:                    var(--light0);
    --graph-node:                    var(--dark3);
    --graph-node-tag:                var(--red);
    --graph-node-attachment:         var(--green);

    --calendar-hover:                var(--red);
    --calendar-background-hover:     var(--light0);
    --calendar-week:                 var(--orange);
    --calendar-today:                var(--orange);

    --dataview-key:                  var(--text-faint);
    --dataview-key-background:       rgba(var(--frost2_x), 0.3);
    --dataview-value:                var(--text-faint);
    --dataview-value-background:     rgba(var(--red_x), 0.3);

    --tab-text-color-focused-active:         var(--frost2);
    --tab-text-color-focused-active-current: var(--red);
}

table
{
    border: 1px solid var(--background-secondary) !important;
    border-collapse: collapse;
}

thead
{
    border-bottom: 2px solid var(--background-modifier-border) !important;
}
  
th
{
    font-weight: 600 !important;
    border: 1px solid var(--background-secondary) !important;
}

td
{
    border-left: 1px solid var(--background-secondary) !important;
    border-right: 1px solid var(--background-secondary) !important;
    border-bottom: 1px solid var(--background-secondary) !important;
}

.markdown-rendered tbody tr:nth-child(even)
{
    background-color: var(--table-row-even-background) !important;
}

.markdown-rendered tbody tr:nth-child(odd)
{
    background-color: var(--table-row-odd-background) !important;
}

.markdown-rendered tbody tr:nth-child(even):hover,
.markdown-rendered tbody tr:nth-child(odd):hover
{
    background-color: var(--table-row-background-hover) !important;
}

.markdown-rendered mark
{
    background-color: var(--text-highlight-bg);
    color: var(--text-highlight-fg);
}

.markdown-rendered mark a
{
    color: var(--red) !important;
    font-weight: 600;
}

.search-result-file-matched-text
{
    color: var(--text-highlight-fg) !important;
}

.cm-hashtag-begin:hover, .cm-hashtag-end:hover
{
    color: var(--text-accent);
    /* background-color: var(--tag-background-hover); */
    text-decoration: underline;
}

input[type=checkbox]
{
    border: 1px solid var(--checkbox-color);
}

input[type=checkbox]:checked
{
    background-color: var(--checkbox-color);
    box-shadow: inset 0 0 0 2px var(--background-primary);
}

input[type=checkbox]:checked:after
{
    display: none;
}

code[class*="language-"],
pre[class*="language-"]
{
    line-height: var(--line-height-tight) !important;
}

.cm-url
{
    color: var(--link-url) !important;
}

.cm-url:hover
{
    color: var(--text-accent-hover) !important;
}

/* Keep highlight/marks the same between viewer and editor. */
.cm-highlight
{
    color: var(--text-highlight-fg) !important;
}

/* Keep inline code the same between viewer and editor. */
.cm-inline-code
{
    border-radius: var(--radius-s);
    font-size: var(--code-size);
    padding: 0.1em 0.25em;
}

.cm-formatting-code + .cm-inline-code
{
    border-radius: 0;
    padding: 0.1em 0;
}

.cm-formatting-code
{
    border-radius: var(--radius-s) 0 0 var(--radius-s);
    padding: 0.1em 0 0.1em 0.25em;
}

.cm-inline-code + .cm-formatting-code
{
    border-radius: 0 var(--radius-s) var(--radius-s) 0;
    padding: 0.1em 0.25em 0.1em 0;
}

.cm-line .cm-strong
{
    color: var(--bold-color) !important;
}

/*
 * Keep list bullet padding the same between viewer and editor.
 * This is annoying with the cursor in the editor as there is a gap.
 */
/*
.cm-formatting-list
{
     padding-right: 4px !important;
}
*/

/*
 * Keep sub-list indenting the same between viewer and editor.
 * This assumes --list-indent is default at 2em.
 */
/*
.cm-indent 
{
    text-indent: 1em !important;
}
*/

.mermaid .note
{
    fill: var(--mermaid-note) !important;
}

.mermaid .loopLine
{
    stroke: var(--mermaid-loopline) !important;
}

.mermaid .loopText>tspan,
.mermaid .entityLabel
{
    fill: var(--red) !important;
}

.mermaid .exclude-range
{
    fill: var(--mermaid-exclude) !important;
}

.mermaid .sequenceNumber
{
    fill: var(--mermaid-seqnum) !important;
}

.calendar .week-num
{
    color: var(--calendar-week) !important;
}

.calendar .today
{
    color: var(--calendar-today) !important;
}

.calendar .week-num:hover,
.calendar .day:hover
{
    color: var(--calendar-hover) !important;
    background-color: var(--calendar-background-hover) !important;
}

.markdown-embed-title
{
    color: var(--yellow);
    font-weight: 600 !important;
}

.cm-active
{
    background-color: var(--cursor-line-background) !important;
}

.nav-file-tag
{
    color: var(--nav-file-tag) !important;
}

.is-flashing
{
    background-color: var(--flashing-background) !important;
}

.dataview.inline-field-key
{
    border-top-left-radius: var(--radius-s);
    border-bottom-left-radius: var(--radius-s);
    padding-left: 4px;
    font-family: var(--font-monospace);
    font-size: var(--font-smaller);
    color: var(--dataview-key) !important;
    background-color: var(--dataview-key-background) !important;
}

.dataview.inline-field-value
{
    border-top-right-radius: var(--radius-s);
    border-bottom-right-radius: var(--radius-s);
    padding-right: 4px;
    font-family: var(--font-monospace);
    font-size: var(--font-smaller);
    color: var(--dataview-value) !important;
    background-color: var(--dataview-value-background) !important;
}

.suggestion-highlight
{
    color: var(--red);
}

