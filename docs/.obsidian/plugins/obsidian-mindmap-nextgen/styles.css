
/* General */

:root {
    --mm-color-text: #fff;
}

[hidden] {
    display: none !important;
}

.mmng-coloring-approach-description {
    border-top: none;
    padding-top: 0;
}

.mmng-coloring-approach-description .setting-item-description { padding-top: 0 }


/* Mindmaps */

.markmap {
    width: 100%;
    height: 100%;
    display: block;
}

.katex-html { display: none }

.workspace-leaf-content[data-type="mindmap-nextgen-plugin"] .view-content {
    padding: 0
}


/* Code blocks inside mindmaps */

/* Remove markmap's code block style */
.markmap-foreign.markmap-foreign code {
    color: inherit;
    background-color: inherit;
}

/* Hide language tag shown by some themes */
.markmap-foreign pre[class*='language-']::after {
    display: none;
}


/* Inline mindmaps */

.block-language-markmap { border-radius: 5px }
.block-language-markmap svg { border-radius: 5px }

.block-language-markmap { letter-spacing: normal }

.mmng-highlight-inline .block-language-markmap {
    background-color: var(--code-background);
    padding: 3px;
}

.markdown-source-view.mod-cm6 .cm-embed-block:has(.block-language-markmap):hover {
    box-shadow: none;
    cursor: auto;
}

.block-language-markmap .workspace-leaf-resize-handle {
    bottom: 0;
    left: 0;
    border-bottom-style: solid;
    border-bottom-width: var(--divider-width);
    height: var(--divider-width-hover);
    width: 100%;
    cursor: row-resize;
}

/* display inline mindmap resize handle */
/* override a rule in the obsidian stylesheet which hides workspace-leaf-resize-handle */
.workspace-split.mod-root
.workspace-leaf:last-child
.workspace-leaf-content
.block-language-markmap
.workspace-leaf-resize-handle
{ display: block }

/* keep resize handle inside block in reading mode */
.block-language-markmap { position: relative }



/* reset button style: begin */
.markdown-source-view.mod-cm6 .edit-block-button.codeblock-settings-button {
    --reset-button-style: true;
    box-shadow: none;
    background-color: inherit;
    height: inherit;
}
/* reset button style: end */

.markdown-source-view.mod-cm6 .edit-block-button.codeblock-settings-button {
    --icon-width: 18px;
    --padding-width: calc(var(--size-2-3) * 2);
    --margin-width: var(--size-2-2);
    right: calc(var(--icon-width) + var(--padding-width) + var(--margin-width));
}

.mmng-settings-modal .modal {
    padding: 0
}

.mmng-settings-modal .modal-content {
    overflow-y: auto;
}

.mmng-settings-modal .modal {
    height: var(--dialog-max-height)
}

.mmng-settings-modal .modal-content :first-child { border-top: 0 }

.modal-close-button { z-index: 2 }

.mmng-settings-tab {
    padding: 0
}
.mmng-level-selector {
    position: sticky;
    top: 0;
    background-color: var(--background-primary);
    z-index: 1;

    padding-top: var(--size-4-8);
    padding-left: var(--size-4-12);
    padding-right: var(--size-4-12);
}

.mmng-level-selector button.mmng-active {
    background-color: var(--interactive-accent);
    color: var(--text-on-accent);
}

.mmng-level-selector .setting-item {
    border-bottom: 1px solid var(--background-modifier-border);
}

.mmng-main {
    padding-left: var(--size-4-12);
    padding-right: var(--size-4-12);
    padding-bottom: var(--size-4-16);
}

.mmng-main .setting-item:first-child {
    padding-top: 0.75em
}

.setting-item.mmng-faded {
    opacity: 40%
}

.setting-item-info.mmng-reset {
    flex: none;
}

.mmng-settings-level-explanation {
    padding-top: var(--size-4-16);
    padding-left: var(--size-4-8);
    padding-right: var(--size-4-8);
}
.mmng-settings-level-explanation .clickable-icon { display: inline-block }

/* Toolbars */

.mm-toolbar {
  position: absolute;
  bottom: 20px;
  left: 20px;
}
