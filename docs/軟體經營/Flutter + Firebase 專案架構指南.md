# Flutter + Firebase 專案架構指南

## 1. 專案結構的核心原則

在使用 Flutter 結合 Firebase 服務時，最佳實踐是**集中管理所有程式碼和設定檔於一個單一的專案資料夾中**。這種方法適用於絕大多數的專案，能有效簡化開發流程、提升效率。

---

## 2. 為什麼選擇單一專案？

- **專案管理簡單**：所有程式碼、Firebase 設定檔 (如 `google-services.json`)、資產和相依套件都集中於一處，便於管理和維護。
    
- **版本控制清晰**：使用 Git 等工具時，所有變更都在一個儲存庫中，歷史紀錄完整，協作時不易出錯。
    
- **跨平台開發優勢**：Flutter 的核心優勢就是「單一程式碼庫」。無論是 iOS、Android 還是 Web 平台，你的核心程式碼都是同一份，只需在必要時進行平台特定調整。
    
- **開發體驗流暢**：在 VS Code 中，你可以輕鬆切換運行裝置，並享有熱重載（Hot Reload）等便利功能，無需切換不同專案。
    

---

## 3. 集中管理所有 Firebase 服務

無論是 **Firestore, Cloud Storage, Firebase Functions, Firebase Hosting,** 或 **Authentication** 等服務，都應該在同一個專案資料夾中管理。

### 專案資料夾範例

以下是一個典型的 Flutter+Firebase 專案結構：

```
my_flutter_app/
├── android/                   # Android 平台設定
├── ios/                       # iOS 平台設定
├── web/                       # Web 平台設定
├── lib/                       # Flutter Dart 程式碼
│   ├── main.dart
│   └── services/              # 服務層，處理與 Firebase 的互動
│       ├── auth_service.dart
│       ├── firestore_service.dart
│       └── storage_service.dart
├── functions/                 # **Firebase Functions 程式碼**
│   ├── node_modules/
│   ├── index.js
│   └── package.json
├── pubspec.yaml               # Flutter 專案相依套件
├── firebase.json              # Firebase Hosting/Functions 部署設定
├── firestore.rules            # Firestore 安全規則
└── storage.rules              # Cloud Storage 安全規則
```

- **Flutter 客戶端**程式碼位於 `lib/`。
    
- **Firebase Functions** 伺服器端程式碼位於 `functions/`。
    
- **Firebase 設定**檔 (如 `firebase.json`) 和**安全規則**檔案則直接位於專案根目錄。
    

---

## 4. Firebase Hosting 整合與部署

將 **Firebase Hosting** 整合到專案中非常簡單，主要透過一個設定檔和指令來完成。

### `firebase.json` 設定檔

在專案根目錄下建立 `firebase.json`，並加入以下內容：

JSON

```
{
  "hosting": {
    "public": "build/web",
    "ignore": [
      "firebase.json",
      "**/.*",
      "**/node_modules/**"
    ],
    "rewrites": [
      {
        "source": "**",
        "destination": "/index.html"
      }
    ]
  }
}
```

- `"public": "build/web"`: 指定 Hosting 服務應部署的檔案來源資料夾。
    
- `"rewrites"`: 確保單頁應用程式（SPA）的路由能夠正常運作，避免 404 錯誤。
    

### 部署流程

1. **編譯 Web 應用程式**：
    
    Bash
    
    ```
    flutter build web
    ```
    
    這會產生 `build/web` 資料夾。
    
2. **部署到 Firebase Hosting**：
    
    Bash
    
    ```
    firebase deploy --only hosting
    ```
    
    如果你也想部署 **Functions**，可以這樣執行：
    
    Bash
    
    ```
    firebase deploy --only hosting,functions
    ```
    

---

## 5. 特殊情境：什麼時候考慮多個專案？

只有在你需要開發兩個**完全獨立**、但可能共用相同 Firebase 後端的應用程式時（例如：一個使用者 App 和一個管理員後台 App），才值得考慮建立兩個獨立的 Flutter 專案。在這種情況下，你需要處理程式碼重複和同步的問題，這會增加維護的複雜度。

**結論**：除非有明確的理由，否則請保持單一專案架構，這將是最高效且最容易管理的做法。