# Firestore 資料結構說明

## 規劃前提

- 所有資料儲存於 firestore
- 卡牌暫時只有偉特塔羅(<PERSON><PERSON> Tarot)及盧恩符文(Runes)
- 系統管理：
  - 牌陣只有管理者可以設置及維護
  - 寫作風格只有管理者可以設置及維護
  - 一般使用者只能使用現有的牌陣和風格
- 卡牌資訊：
	- 牌種（waite, runes）
	- 代號
		- 大阿納卡：M00-M21
		- 小阿納卡：
				- 杯：C01-C10,CCP,CCN,CCQ,CCK
				- 幣：P01-P10,PCP,PCN,PCQ,PCK
				- 劍：S01-S10,SCP,SCN,SCQ,SCK
				- 杖：W01-W10,WCP,WCN,WCQ,WCK
		- [[盧恩符文]]
- 牌陣資訊：
	- 牌陣只有管理者可以設置，其他人則可以選用
	- 牌陣中牌種可以混用，但排序為塔羅在前、盧恩在後
	- 作用欄位使用 object.property 的方式描述，可以使用*
	- 牌陣原則上同類型但可能互相衝突的屬性設定為一個牌陣，因為抽牌的牌是不會重複的
	- 欄位：
		- 牌陣名稱
		- 含義list：牌陣中每一張牌代表的意思
		- 牌種list：每一張牌的牌種
		- 作用欄位list：作用在哪些欄位
- 欄位抽牌：
	- 牌陣名稱
	- 【牌種、代號、正逆】
	- 解讀：AI依照牌陣設定的含義解讀

## 通用結構

### 抽牌結構
```typescript
interface SpreadReading {
  spreadName: string;   // 牌陣名稱
  readings: {
    cardType: string;   // 牌種（塔羅/盧恩）
    meaning: string;   // 含義
    card: string;      // 卡牌
    orientation: string; // 正逆位 "upright" | "reversed"，預留擴展性，不使用 boolean
  }[];
  interpretation: string; // 解讀
}
```

## 集合結構概述

### 使用者集合 (user)
```typescript
{
  id: string;              // Firebase Auth UID
  display_name: string;    // 顯示名稱
  email: string;          // 電子郵件
  created_at: timestamp;  // 建立時間
  updated_at: timestamp;  // 更新時間
  user_role?: string;     // admin 用這個欄位來判定是否要顯示管理者專用選單
  
  // 作者相關資料
  pen_name: string;       // 作者筆名
  reference_work: string; // 參考作品
  writing_style: string;  // 寫作風格描述
  language: string;       // 創作語言（例如：zh-TW, en-US）
  llm_settings: {         // LLM設定
    provider: string;     // 服務商名稱
    api_key: string;      // API金鑰
    model: string;        // 使用模型
  }[];
}
```

### 共用角色集合 (shared_role)
```typescript
{
  id: string;            // 角色ID
  user_id: string;       // 使用者ID（作者）
  species: string;       // 物種
  name: string;          // 姓名
  age: number;           // 年齡
  birthday: timestamp;   // 生日（選填）
  notes: string;         // 註記
  appearance: string;    // 外貌描述
  
  // 以下使用抽牌結構
  background_events: SpreadReading;    // 背景資料
  personality_traits: SpreadReading;   // 性格特徵
  goals: SpreadReading;               // 目標層次
  signatures: SpreadReading;          // 標誌性特質
  
  astrology: {           // 角色生日分析
    birth_chart: string; // 占星分析
    destiny: string;     // 命運解讀
  };
  
  experiences: {         // 經歷
    work_id: string;     // 作品ID
    experience: string;  // 重要經歷描述
    final_state: string; // 最終狀態
  }[];
}
```

### 共用物品集合 (shared_item)
```typescript
{
  id: string;           // 物品ID
  user_id: string;      // 使用者ID
  name: string;         // 名稱
  appearance: string;   // 外貌描述
  legend: string;       // 典故
  experiences: {        // 經歷
    work_id: string;    // 作品ID
    experience: string; // 重要經歷
    final_state: string;// 最終狀態
  }[];
}
```

### 寫作風格集合 (writing_style)
```typescript
{
  id: string;          // 風格ID
  name: string;        // 風格名稱
  description: string; // 風格描述
}
```

### 牌陣集合 (spread)
```typescript
{
  id: string;         // 牌陣ID
  name: string;       // 牌陣名稱
  positions: {        // 卡牌位置
    card_type: string;// 牌種
    meaning: string;  // 含義
  }[];
  applicable_fields: string[]; 
    // 適用欄位，例如：
    // ["shared_role.background_events", "shared_role.personality_traits",
    //  "shared_role.goals", "shared_role.signatures",
    //  "work.world_view", "work.main_plot",
    //  "chapter.spread"]
}
```

### 作品集合 (work)
```typescript
{
  id: string;           // 作品ID
  user_id: string;      // 使用者ID
  title: string;        // 故事名稱
  language: string;     // 作品語言（例如：zh-TW, en-US）
  cover_image?: string; // 封面圖片URL
  summary?: string;     // 作品簡介
  target_audience: string; // 目標讀者
  status: string;       // 作品狀態（draft, serializing, completed）
  chapter_count: number;// 章節數量
  last_updated: timestamp; // 最後更新時間
  created_at: timestamp;// 創建時間
  is_deleted: boolean;  // 軟刪除標記
}
```

## 頂層集合結構

### 作品詳情集合 (work_detail)
```typescript
{
  id: string;          // 與 work.id 相同
  work_id: string;     // 作品ID
  style: string;       // 作品風格
  setting: string;     // 地點設定
  length: string;      // 作品長度
  
  world_view: SpreadReading;         // 世界觀
  main_plot: SpreadReading;          // 主線大綱
  
  story_style: string; // 故事風格
  story_type: string;  // 故事類型
  background: string;  // 故事背景
  goal: string;        // 故事目標
  
  roles: {
    role_code: string; // 角色代號
    shared_role_id?: string; // 共用角色ID（可選）
    custom_role?: {    // 自創角色資料
      // 同 shared_role 結構
    };
  }[];
  
  items: {
    shared_item_id?: string; // 共用物品ID（可選）
    custom_item?: {    // 自創物品資料
      // 同 shared_item 結構
    };
  }[];
  
  relationships: string; // 人物關係描述
}
```

### 章節集合 (chapter)
```typescript
{
  id: string;          // 章節ID
  work_id: string;     // 作品ID
  title: string;       // 章節標題
  order: number;       // 章節順序
  role_codes: string[];// 參與角色代號
  item_ids: string[]; // 參與物品ID
  
  spread: SpreadReading; // 牌陣
  
  content: string;     // 章節內容
  summary: string;     // 章節摘要
  
  created_at: timestamp; // 創建時間
  updated_at: timestamp; // 更新時間
  
  current_version: string; // 當前版本ID
  is_deleted: boolean;    // 軟刪除標記
}
```

### 章節版本集合 (chapter_version)
```typescript
{
  id: string;         // 版本ID
  chapter_id: string; // 章節ID
  work_id: string;    // 作品ID
  role_codes: string[]; // 參與角色代號
  item_ids: string[]; // 參與物品ID
  spread: SpreadReading; // 牌陣
  content: string;    // 章節內容
  summary: string;    // 章節摘要
  commit_message: string; // 提交訊息
  created_at: timestamp; // 創建時間
  is_autosave: boolean; // 是否為自動儲存版本
}
```

### 評論集合 (review)
```typescript
{
  id: string;         // 評論ID
  work_id: string;    // 作品ID
  reviewer_type: string; // 評論者類型
  content: string;    // 評論內容
  rating?: number;    // 評分（可選）
  created_at: timestamp; // 建立時間
  updated_at: timestamp; // 更新時間
}
```

## 索引建議

### 必要索引
1. work: user_id, status
2. work: user_id, last_updated
3. shared_role: user_id
4. shared_item: user_id
5. chapter: work_id, order
6. chapter_version: chapter_id, created_at

### 複合索引
1. work: user_id + created_at
2. work: user_id + last_updated
3. chapter: work_id + role_codes
4. review: work_id + reviewer_type

## 安全規則建議

```typescript
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // 檢查是否為管理者
    function isAdmin() {
      return exists(/databases/$(database)/documents/admin/$(request.auth.uid));
    }
    
    // 系統管理集合只允許管理者寫入
    match /spread/{id} {
      allow read: if request.auth.uid != null;
      allow write: if isAdmin();
    }
    
    match /writing_style/{id} {
      allow read: if request.auth.uid != null;
      allow write: if isAdmin();
    }
    
    // 使用者只能讀取/修改自己的資料
    match /user/{id} {
      allow read: if request.auth.uid == id;
      allow write: if request.auth.uid == id;
    }
    
    // 作品相關規則
    match /work/{id} {
      allow read: if request.auth.uid != null;
      allow write: if request.auth.uid == resource.data.user_id;
    }
    
    // 作品詳情
    match /work_detail/{id} {
      allow read: if request.auth.uid != null;
      allow write: if request.auth.uid == get(/databases/$(database)/documents/work/$(id)).data.user_id;
    }
    
    // 章節
    match /chapter/{id} {
      allow read: if request.auth.uid != null;
      allow write: if request.auth.uid == get(/databases/$(database)/documents/work/$(resource.data.work_id)).data.user_id;
    }
    
    // 評論
    match /review/{id} {
      allow read: if request.auth.uid != null;
      allow create: if request.auth.uid != null;
      allow update, delete: if request.auth.uid == resource.data.user_id;
    }
    
    // 共用角色和物品規則
    match /shared_role/{id} {
      allow read: if request.auth.uid != null;
      allow write: if request.auth.uid == resource.data.user_id;
    }
    
    match /shared_item/{id} {
      allow read: if request.auth.uid != null;
      allow write: if request.auth.uid == resource.data.user_id;
    }
  }
}
```

## 注意事項

1. 所有時間戳記使用 Firestore 的 `timestamp` 類型
2. 所有 ID 欄位使用自動生成的 UUID
3. 所有集合名稱使用單數形式
4. 所有欄位名稱使用 snake_case
5. 避免過深的文檔嵌套，改用頂層集合
6. 實作軟刪除機制，使用 `is_deleted` 欄位
7. 關聯查詢使用複合索引優化
8. 批次操作使用 batch 或 transaction
9. 文檔大小限制在 1MB 以內
10. 章節版本控制建議：
    - 重要時間點手動提交版本
    - 每次提交需要填寫提交訊息
    - 自動儲存版本定期清理
    - 手動提交版本永久保存 