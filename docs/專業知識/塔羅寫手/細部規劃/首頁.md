# 首頁設計

## 功能概述
- 顯示使用者的作品列表
- 快速創建新作品
- 顯示系統公告（如果有）

## 佈局設計

### Desktop 版本（基本型）
- 左側：NavigationRail
  - 首頁
  - 設定
  - 角色
  - 物品
- 右側內容區域（雙欄式）
  - 作品列表區域
    - 作品卡片網格佈局
    - 新增作品按鈕（固定右下角）
  - 系統公告區域（如果有）

### Mobile 版本（基本型）
- 完整佔用內容區
- 底部：BottomNavigationBar
- 使用 Get.to() 進行頁面切換
- 作品列表採用單欄式卡片佈局
- 新增作品按鈕固定在右下角

## 元件說明

### AppBar
- Desktop版本：
  - 左側：應用程式名稱
  - 右側：
    - 使用者頭像（點擊顯示選單）
    - 設定按鈕
- Mobile版本：
  - 中間：應用程式名稱
  - 右側：使用者頭像

### 作品卡片
- 顯示內容：
  - 作品標題
  - 最後更新時間
  - 作品狀態（草稿/連載中/已完結）
  - 章節數量
  - 作品簡介（限制顯示行數）
- 點擊行為：
  - 進入作品編輯頁面

### 新增作品按鈕
- 固定在右下角
- 點擊行為：
  - 開啟新增作品對話框
