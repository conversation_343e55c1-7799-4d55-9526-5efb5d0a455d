# 設定頁設計

## 功能概述
- 管理使用者個人資料
- 設定作品語言
- 設定寫作風格
- 設定 LLM 服務

## 佈局設計

### Desktop 版本（基本型）
- 左側：NavigationRail
- 右側內容區域（雙欄式）
  - 左側：設定選項列表
    - 個人資料
    - 語言設定
    - LLM服務
  - 右側：設定內容區域

### Mobile 版本（基本型）
- 完整佔用內容區
- 底部：BottomNavigationBar
- 使用 Get.to() 進行頁面切換
- 設定選項採用列表形式
- 點擊選項後跳轉到對應設定頁面

## 元件說明

### 個人資料區塊
- Desktop版本：
  - 表單佈局，左側標籤右側輸入
  - 即時預覽更改效果
- Mobile版本：
  - 垂直堆疊佈局
  - 標籤在上方，輸入框佔滿寬度
- 共同元件：
  - 筆名輸入框
  - 參考作品上傳區域（支援 TXT、DOCX）
  - 寫作風格描述輸入框
  - 寫作語言輸入框

### LLM 服務設定區塊
- Desktop版本：
  - 表格式佈局
  - API Key 輸入與驗證按鈕並排
- Mobile版本：
  - 垂直堆疊佈局
  - API Key 輸入框下方為驗證按鈕
- 共同元件：
  - 服務商下拉選單
  - API Key 輸入框（加密儲存）
  - 驗證按鈕
  - 模型選擇下拉選單

### 語言設定說明
- 寫作語言設定影響：
  - 介面多國語言設定
  - AI 創作語言
- 注意事項：
  - 若有其他語言需求，建議待作品完成再使用特定語言風格翻譯
  - 寫作語言選項比介面語言選項更多
