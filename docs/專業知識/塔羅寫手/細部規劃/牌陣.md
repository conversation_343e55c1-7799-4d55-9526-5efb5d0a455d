# 牌陣頁面設計

## 功能概述
- 管理牌陣列表（僅管理員）
- 查看牌陣詳情
- 設定牌陣適用欄位

## 佈局設計

### Desktop 版本（基本型）
- 左側：NavigationRail
- 右側內容區域（雙欄式）
  - 左側：牌陣列表區
    - 牌陣卡片網格
    - 新增牌陣按鈕（管理員專用）
  - 右側：牌陣詳情區
    - 基本資料表單
    - 卡牌位置編輯器
    - 適用欄位選擇器

### Mobile 版本（基本型）
- 完整佔用內容區
- 底部：BottomNavigationBar
- 使用 Get.to() 進行頁面切換
- 牌陣列表採用垂直卡片佈局
- 牌陣詳情使用全屏頁面

## 頁面元件

### AppBar
- Desktop版本：
  - 左側：頁面標題
  - 右側：功能按鈕（管理員專用）
- Mobile版本：
  - 中間：頁面標題
  - 右側：功能按鈕（管理員專用）

### 牌陣列表區
- Desktop版本：
  - 網格式卡片佈局
  - 新增按鈕固定右下角（管理員專用）
- Mobile版本：
  - 垂直列表佈局
  - 新增按鈕固定右下角（管理員專用）
- 共同元件：
  - 牌陣卡片
    - 牌陣名稱
    - 卡牌數量
    - 適用欄位數量

### 牌陣詳情區
- Desktop版本：
  - 側邊面板佈局
  - 分區塊顯示資料
- Mobile版本：
  - 全屏頁面
  - Tab式切換資料區塊
- 共同元件：
  - 基本資料區塊
    - 牌陣名稱輸入
    - 牌陣描述輸入
  - 卡牌位置區塊
    - 位置列表
      - 位置編號
      - 牌種選擇
      - 含義輸入
  - 適用欄位區塊
    - 樹狀結構選擇器

### 卡牌位置編輯器
- Desktop版本：
  - 表格式佈局
  - 即時預覽效果
- Mobile版本：
  - 垂直列表佈局
  - 可展開/收合詳情
- 共同功能：
  - 動態新增/刪除位置
  - 拖曳排序
  - 批量編輯

### 適用欄位選擇器
- Desktop版本：
  - 樹狀結構顯示
  - 多列顯示
- Mobile版本：
  - 折疊式樹狀結構
  - 單列顯示
- 共同功能：
  - 支援多選
  - 支援萬用符號
  - 即時搜索過濾
