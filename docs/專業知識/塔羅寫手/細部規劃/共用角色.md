# 共用角色頁面設計

## 功能概述
- 管理共用角色列表
- 創建新角色
- 編輯現有角色
- 查看角色經歷

## 佈局設計

### Desktop 版本（基本型）
- 左側：NavigationRail
- 右側內容區域（雙欄式）
  - 左側：角色列表區
    - 角色卡片網格
    - 新增角色按鈕
  - 右側：角色詳情區
    - 基本資料表單
    - 塔羅牌解讀區域

### Mobile 版本（基本型）
- 完整佔用內容區
- 底部：BottomNavigationBar
- 使用 Get.to() 進行頁面切換
- 角色列表採用垂直卡片佈局
- 角色詳情使用全屏頁面

## 頁面元件

### AppBar
- Desktop版本：
  - 左側：頁面標題
  - 右側：功能按鈕
- Mobile版本：
  - 中間：頁面標題
  - 右側：功能按鈕

### 角色列表區
- Desktop版本：
  - 網格式卡片佈局
  - 新增按鈕固定右下角
- Mobile版本：
  - 垂直列表佈局
  - 新增按鈕固定右下角
- 共同元件：
  - 角色卡片
    - 角色名稱
    - 性別年齡
    - 外貌描述（限制行數）
    - 最近經歷

### 角色詳情區
- Desktop版本：
  - 側邊面板佈局
  - 分區塊顯示資料
- Mobile版本：
  - 全屏頁面
  - Tab式切換資料區塊
- 共同元件：
  - 基本資料區塊
    - 姓名、性別、年齡輸入
    - 外貌描述輸入
  - 背景資料區塊（塔羅牌）
    - 童年經歷
    - 家庭關係
    - 教育背景
    - 重要事件
  - 性格特徵區塊（塔羅牌）
    - 優點
    - 缺點
    - 內在衝突
  - 目標層次區塊（塔羅牌）
    - 表面目標
    - 深層目標
    - 潛意識目標
  - 標誌性特質區塊（塔羅牌）
    - 標誌性行為
    - 口號語癖
    - 魅力點
  - 占星分析區塊
    - 生日設定
    - 命運解讀

### 塔羅牌抽取區域
- Desktop版本：
  - 內嵌在詳情區塊中
  - 可收合展開
- Mobile版本：
  - 獨立頁面
  - 全屏顯示
- 共同元件：
  - 牌陣說明
  - 抽牌按鈕
  - 抽牌結果顯示
  - 解讀結果輸入框
