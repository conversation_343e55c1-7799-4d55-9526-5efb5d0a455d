# 寫作風格頁面設計

## 功能概述
- 管理寫作風格列表（僅管理員）
- 查看風格詳情
- 選擇作品使用的風格

## 佈局設計

### Desktop 版本（基本型）
- 左側：NavigationRail
- 右側內容區域（雙欄式）
  - 左側：風格列表區
    - 風格卡片網格
    - 新增風格按鈕（管理員專用）
  - 右側：風格詳情區
    - 基本資料表單
    - 風格特徵編輯器
    - 範例段落編輯器

### Mobile 版本（基本型）
- 完整佔用內容區
- 底部：BottomNavigationBar
- 使用 Get.to() 進行頁面切換
- 風格列表採用垂直卡片佈局
- 風格詳情使用全屏頁面

## 頁面元件

### AppBar
- Desktop版本：
  - 左側：頁面標題
  - 右側：功能按鈕（管理員專用）
- Mobile版本：
  - 中間：頁面標題
  - 右側：功能按鈕（管理員專用）

### 風格列表區
- Desktop版本：
  - 網格式卡片佈局
  - 新增按鈕固定右下角（管理員專用）
- Mobile版本：
  - 垂直列表佈局
  - 新增按鈕固定右下角（管理員專用）
- 共同元件：
  - 風格卡片
    - 風格名稱
    - 風格簡短描述
    - 使用次數統計

### 風格詳情區
- Desktop版本：
  - 側邊面板佈局
  - 分區塊顯示資料
- Mobile版本：
  - 全屏頁面
  - Tab式切換資料區塊
- 共同元件：
  - 基本資料區塊
    - 風格名稱輸入
    - 風格描述輸入
  - 風格特徵區塊
    - 語言風格輸入
    - 敘事特點輸入
    - 情節偏好輸入
    - 人物刻畫輸入
  - 範例段落區塊
    - 示例文本編輯器

### 風格特徵編輯器
- Desktop版本：
  - 表格式佈局
  - 即時預覽效果
- Mobile版本：
  - 垂直列表佈局
  - 可展開/收合詳情
- 共同功能：
  - 多個文本輸入區域
  - 支援格式化文本
  - 支援範例展示

### 範例段落編輯器
- Desktop版本：
  - 分欄式佈局
  - 即時預覽
- Mobile版本：
  - 標籤切換式佈局
  - 預覽按鈕
- 共同功能：
  - Markdown 支援
  - 格式化工具列
  - 字數統計
