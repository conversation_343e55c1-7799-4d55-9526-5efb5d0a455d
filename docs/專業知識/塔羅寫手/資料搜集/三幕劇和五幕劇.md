三幕劇和五幕劇是戲劇和小說創作中常見的結構框架，用來組織故事的發展，讓情節有清晰的起伏和邏輯性。以下是它們的定義和說明：

---

### 三幕劇 (Three-Act Structure)
三幕劇是最常見的敘事結構之一，特別在電影、小說和戲劇中廣泛應用。它將故事分為三個主要部分，每部分有特定的功能：

1. **第一幕 (Act 1) - 開場 (Setup)**  
   - **功能**：介紹故事的世界、角色和核心衝突。
   - **內容**：
     - 建立主要角色、背景和日常生活的基調。
     - 出現「引發事件」(Inciting Incident)，打破現狀，推動主角進入故事主線。
     - 通常以一個「轉折點」(Plot Point)結束，主角被迫做出重大決定或進入新階段。
   - **例子**：在一部奇幻小說中，第一幕可能介紹主角的平凡村莊生活，直到怪獸襲擊迫使他踏上冒險。

2. **第二幕 (Act 2) - 對抗 (Confrontation)**  
   - **功能**：發展衝突，讓主角面對挑戰並成長。
   - **內容**：
     - 這是最長的一幕，主角試圖解決問題，但面臨越來越多的障礙。
     - 中間點 (Midpoint) 通常帶來重大轉折，提升緊張感。
     - 以另一個「轉折點」結束，主角看似接近成功或陷入絕望。
   - **例子**：主角學習魔法對抗怪獸，但發現敵人比想像中強大，甚至背後有更大的陰謀。

3. **第三幕 (Act 3) - 結局 (Resolution)**  
   - **功能**：解決衝突，完成故事弧。
   - **內容**：
     - 高潮 (Climax)：主角與主要對手的最終對決。
     - 收尾：展示衝突解決後的結果，角色的改變或新常態。
   - **例子**：主角擊敗怪獸，拯救村莊，並找到內心的勇氣，回到平靜但有所成長的生活。

**特點**：三幕劇結構簡單明快，適合大多數線性故事，特別是注重高潮和結局的作品。

---

### 五幕劇 (Five-Act Structure)
五幕劇起源於古希臘戲劇（如莎士比亞的劇作），比三幕劇更細緻，將故事分為五個階段，強調情節的逐步推進和高潮的層次感：

1. **第一幕 (Act 1) - 引言 (Exposition)**  
   - **功能**：設定舞台，介紹背景和衝突的起因。
   - **內容**：類似三幕劇的第一幕，聚焦於世界觀和角色的初次亮相。
   - **例子**：展示主角的貴族家庭因政治陰謀而分裂。

2. **第二幕 (Act 2) - 上升行動 (Rising Action)**  
   - **功能**：衝突開始升溫，推動故事向前。
   - **內容**：介紹次要衝突或障礙，角色開始採取行動應對問題。
   - **例子**：主角發現父親被暗殺，決定調查真相。

3. **第三幕 (Act 3) - 高潮 (Climax)**  
   - **功能**：故事達到情感或行動的高峰。
   - **內容**：主角面對關鍵挑戰，但結果尚未明朗，通常帶來重大轉折。
   - **例子**：主角與敵人初次對決，卻發現敵人是自己的兄弟。

4. **第四幕 (Act 4) - 下降行動 (Falling Action)**  
   - **功能**：高潮後的後果展開，衝突逐漸解開。
   - **內容**：角色處理高潮的影響，新的問題或懸念可能浮現。
   - **例子**：主角因內心矛盾猶豫是否殺死兄弟，同時敵方準備反撲。

5. **第五幕 (Act 5) - 結局 (Denouement)**  
   - **功能**：解決所有衝突，完成故事。
   - **內容**：揭示最終結局，角色命運塵埃落定。
   - **例子**：主角選擇寬恕兄弟，聯手擊敗幕後黑手，家族重歸和平。

**特點**：五幕劇更注重情節的層次感和複雜性，適合需要多重轉折或深層角色發展的故事。

---

### 三幕劇與五幕劇的比較
- **長度與複雜性**：
  - 三幕劇更簡潔，適合短篇或單一主題的故事。
  - 五幕劇更細膩，適合長篇或多線故事。
- **高潮位置**：
  - 三幕劇的高潮在第三幕，接近結局。
  - 五幕劇的高潮在第三幕，後面還有餘韻和收尾。
- **應用場景**：
  - 三幕劇常用於好萊塢電影和現代小說。
  - 五幕劇多見於古典戲劇或需要史詩感的作品。

---

### 對「Tarot Weaver」的啟發
如果將這些結構融入軟體：
- **三幕劇**：可以用三張塔羅牌生成「開場-對抗-結局」，快速生成短篇。
- **五幕劇**：可以用五張牌生成「引言-上升-高潮-下降-結局」，適合系列故事或長篇。
這樣的結構能幫助使用者更靈活地設計故事，並滿足不同創作需求！