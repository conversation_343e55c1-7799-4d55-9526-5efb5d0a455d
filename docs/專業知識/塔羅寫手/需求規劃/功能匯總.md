# Tarot Weaver軟體功能列表

## 軟體目標
- 使用塔羅牌或盧恩符文應用LLM解讀並自動創作小說
- 透過IP管理，可創造出如漫威宇宙大部頭系列小說
- IP細節可望創造出偶像，提升附加價值

## 系統設定
- 各LLM服務設定
- 🃏塔羅或盧恩
- ⌨️ 使用者自訂

## 作者設定
### 作家風格
- **功能描述**：上傳寫作樣本供LLM學習。
- **輸入**：
	- 文字檔案：TXT、DOCX
	- 樣本描述：文字輸入（可選）
- **輸出**：風格參數儲存。
- 預設風格

### 共同人物管理
#### IP管理
- **基本資訊**：
	- 姓名：文字輸入（必填）
	- 性別：（男、女、其他）
	- 年齡：數字輸入
	- 註記：這個角色的基本設定
	- 外貌描述：🃏文字輸入（例如「金髮碧眼，帶銀色項鍊」）
- **角色背景深度**：
	- 童年經歷：🃏（關鍵事件）
	- 家庭關係：🃏（親人影響）
	- 教育背景：🃏（技能來源）
	- 重要人生事件：🃏（轉折點）
- **性格複雜性**：
	- 優點：🃏
	- 缺點：🃏
	- 內在衝突：🃏（道德掙扎）
	- 隱藏慾望與恐懼：🃏
- **目標層次性**：
	- 表面目標：🃏
	- 深層目標：🃏
	- 潛意識目標：🃏
- **標誌性特質（偶像化）**：
	- 標誌性行為：🃏（例如「總是摸項鍊沉思」）
	- 口號或語癖：🃏（例如「命運由我掌握」）
	- 魅力點：🃏（例如「深邃眼神」）
- **設定人物生日**：
	- 分析所有設定資料，用占星術或八字設定人物的出生日期
	- 由所設定日期反推人物的設定，甚至大運
	- 這有助於強化IP的獨特性
- 所有設定都可有可無，依照實際需求調整
- 於單篇文章創作完成，紀錄「文章」+「人物經歷、時間點的狀態」

#### 物品管理
- 可用於系列文章的物品
- 基本資訊：
	- 名稱
	- 外貌
	- 典故
- 於單篇文章創作完成，紀錄「文章」+「物品時間點的狀態」

## 文章設定
### 基本創作設定
- **功能描述**：設定故事基本屬性。
- **輸入**：
	- 故事名稱：先設定，之後可以用AI創造
	- 作家風格模仿：（我的風格、預設風格）
	- 目標讀者：（青少年、成人、通用）
	- 地點：⌨️（古代、現代、未來、修仙、自訂）
	- 長度：（短篇1000字、中篇5000字、長篇2萬字+）
	- 風格：🃏⌨️（幽默、嚴肅、詩意、自訂）
	- 故事類型：🃏⌨️（奇幻、科幻、愛情、懸疑）
	- 世界觀設定：🃏⌨️生成故事世界觀。
	- 故事背景設定：⌨️（歷史事件、神話傳說、科學概念、地理、歷史、文化）
	- 故事目標：⌨️（描述預想的結果，劇情最終發展結果）
	- **預設模板**：新增「三幕劇」和「五幕劇」的快捷選項，讓新手能一鍵選擇結構。

### 角色設定
- 多角色設定，可從IP選入
	- 普通角色的設置內容與IP相同，但部分可忽略
	- 🃏創造角色
- 從故事中抽出IP角色
	- **功能描述**：提取生成角色為IP。
	- 補充細化：外貌、背景、性格、目標、特質
- 人物關係描述：⌨️

### 物品設定
- 基本資訊：名稱、外貌
- 隨機：🃏創造物品
- 紀錄「物品時間點的狀態」

### 主線大綱（全局）
- **功能描述**：生成故事主線大綱。
- **牌陣**：🃏起承轉合（四張牌）
- 可以提供修改意見，再重新產生

### 章節發展
- **功能描述**：為每章生成結構。
- 章節數量：根據長度分配
- 主角參與：多選框（優先顯示系列IP角色）
- 物品參與
- **牌陣**：🃏起承轉合 + 場景（五張牌）
- 可以提供修改意見，再重新產生
- 摘要：保存章節摘要，供情節發展連續性參考

### 設定故事名稱
- 故事名稱需要反映故事的核心概念或主題，但必須在創作過程中適當時機生成，避免過早定調影響靈活性
- 建議在生成核心概念與主要情節大綱後，但細節內容生成前


---

## 結果調整模組
- 手稿
- 預覽
	- 產生於內容生成結束產生時間序以供檢驗
	- 一經調整變回手稿
	- AI邏輯審查：
		- 故事邏輯和情節合理性
		- 自動審查時間序
	- AI文稿評論員：提供多種文稿評論員，為文稿評分
		- 文學評論家
		- 類型小說愛好者（依故事設定）
		- 普通讀者
		- 編輯或出版商
		- 心理學家或社會學家
		- 中國短劇觀眾
		- 考慮：允許用戶自定義評論標準（如重視邏輯性或文風），提升評論的針對性和實用性。
- 完稿
	- 完稿後鎖定
	- 保存共同IP、物品經歷及狀態
	- 輸出格式：（Markdown、PDF、Word）
	- 可以解鎖回到手稿狀態


