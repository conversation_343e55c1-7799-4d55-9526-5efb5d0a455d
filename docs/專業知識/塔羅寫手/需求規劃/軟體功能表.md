## 軟體功能列表

### 1. 用戶設定模組
#### 1.1 基本創作設定
- **功能描述**：設定故事基本屬性與作家風格。
- **輸入**：
  - 故事類型：下拉選單（奇幻、科幻、愛情、懸疑）
  - 風格：下拉選單（幽默、嚴肅、詩意）
  - 長度：下拉選單（短篇1000字、中篇5000字、長篇2萬字+）
  - 作家風格模仿：開關 + 下拉選單（「我的風格」或預設風格）
- **選擇項**：
  - 預設值：奇幻、嚴肅、中篇、風格關閉
- **輸出**：設定儲存至Firestore與本地資料庫。

#### 1.2 作家風格樣本上傳
- **功能描述**：上傳樣本讓LLM學習風格。
- **輸入**：
  - 文字檔案：TXT、DOCX（建議500-2000字）
  - 樣本描述：文字輸入（可選）
- **選擇項**：
  - 儲存風格：按鈕
  - 覆蓋舊風格：開關
- **輸出**：風格參數儲存。

#### 1.3 進階創作設定（可選）
- **功能描述**：提供進階選項。
- **輸入**：
  - 地點：下拉選單（古代、現代、未來）
  - 目標讀者：下拉選單（青少年、成人、通用）
  - 主角數量：數字輸入（預設1，最多5）
  - **系列故事模式**：開關（啟用後優先使用IP角色作為核心）
- **選擇項**：
  - 可留空，使用預設值。
- **輸出**：設定儲存。

---

### 2. 塔羅靈感模組
#### 2.1 核心概念與主題牌陣
- **功能描述**：生成故事主題。
- **牌陣**：三張牌（主題、類型/風格、挑戰/機遇）
- **輸入**：用戶點擊「抽牌」。
- **選擇項**：
  - 正逆位：開關（預設關閉）
- **輸出**：牌面與解讀儲存。

#### 2.2 世界觀設定牌陣
- **功能描述**：生成故事世界觀。
- **牌陣**：六張牌（地點、時間、物理法則、社會結構、文化習俗、重要場景）
- **輸入**：自動抽牌。
- **選擇項**：
  - 重新抽牌：按鈕
- **輸出**：世界觀資料儲存。

#### 2.3 角色定位牌陣（支援多主角）
- **功能描述**：為主角生成基礎設定。
- **牌陣**：四張牌（性格、背景、目標、挑戰）
- **輸入**：
  - 主角數量：根據進階設定
  - 自訂姓名：文字輸入（可選）
  - IP角色選項：下拉選單（選擇現有IP或新建）
- **選擇項**：
  - 角色類型：下拉選單（主角、反派、配角）
  - 新增主角：按鈕
- **輸出**：牌陣結果儲存。

#### 2.4 系列IP主角創建（一開始創作）
- **功能描述**：創建細緻的IP角色，作為系列故事主角或偶像。
- **輸入**：
  - **基本資訊**：
    - 姓名：文字輸入（必填）
    - 性別：下拉選單（男、女、其他）
    - 年齡：數字輸入
    - 外貌描述：文字輸入（例如「金髮碧眼，帶銀色項鍊」）
  - **角色背景深度**：
    - 童年經歷：文字輸入或牌陣（關鍵事件）
    - 家庭關係：文字輸入或牌陣（親人影響）
    - 教育背景：文字輸入或牌陣（技能來源）
    - 重要人生事件：文字輸入或牌陣（轉折點）
  - **性格複雜性**：
    - 優點：文字輸入或牌陣
    - 缺點：文字輸入或牌陣
    - 內在衝突：文字輸入或牌陣（道德掙扎）
    - 隱藏慾望與恐懼：文字輸入或牌陣
  - **目標層次性**：
    - 表面目標：文字輸入或牌陣
    - 深層目標：文字輸入或牌陣
    - 潛意識目標：文字輸入或牌陣
  - **標誌性特質（偶像化）**：
    - 標誌性行為：文字輸入（例如「總是摸項鍊沉思」）
    - 口號或語癖：文字輸入（例如「命運由我掌握」）
    - 魅力點：文字輸入（例如「深邃眼神」）
  - **牌陣選擇**：開關（是否用塔羅生成）
  - 自動生成生日：開關（啟用後根據特質生成）
    - 手動輸入生日：日期選擇器（可選）
	- **輸出**：
	    - 生日與星座分析附加至IP資料。
	    - 範例輸出：
	        - 姓名：衛斯理
	        - 生日：1185年8月15日，上午6點
	        - 星座：太陽獅子、月亮雙魚、上升射手
	        - 解釋：勇敢領袖，內心敏感，外顯冒險魅力
- **牌陣**（若啟用）：
  - 六張牌陣：
    - 牌1：童年經歷
    - 牌2：性格優點
    - 牌3：性格缺點
    - 牌4：深層目標
    - 牌5：內在衝突
    - 牌6：標誌性特質
- **選擇項**：
  - 儲存至IP庫：按鈕
  - 公開分享：開關（預設關閉）
  - 立即使用：開關（加入當前故事）
- **輸出**：
  - 細緻的IP角色模板儲存至Firestore。
  - 範例：創建「衛斯理」，金髮劍士，童年失去雙親，目標探索未知，標誌性行為「輕撫劍柄」。

#### 2.5 從故事中抽出系列IP角色
- **功能描述**：從生成角色中提取並升級為系列IP主角。
- **輸入**：
  - 角色選擇：下拉選單（當前故事角色）
  - **補充細化**：
    - 外貌描述：文字輸入
    - 背景深度：新增童年、家庭等細節
    - 性格複雜性：新增優點、缺點等
    - 目標層次：新增深層目標等
    - 標誌性特質：新增偶像化元素
- **選擇項**：
  - 儲存至IP庫：按鈕
  - 公開分享：開關
- **輸出**：
  - 角色升級為系列IP，儲存至Firestore。
  - 範例：故事生成「莉娜」，提取後補充外貌「紅髮綠眼」、標誌性行為「哼唱老歌」。

#### 2.6 系列IP成長軌跡管理
- **功能描述**：記錄IP角色在多故事中的成長與變化。
- **輸入**：
  - IP角色選擇：下拉選單
  - 新故事事件：文字輸入或自動從故事提取（例如「衛斯理找到神劍」）
  - 成長變化：文字輸入（例如「更堅定」）
- **選擇項**：
  - 更新IP資料：按鈕
- **輸出**：
  - IP角色的成長軌跡儲存，供後續故事參考。

#### 2.7 情節結構牌陣（全局）
- **功能描述**：生成故事主線大綱。
- **牌陣**：起承轉合（四張牌）
- **輸入**：自動抽牌。
- **選擇項**：
  - 重新抽牌：按鈕
- **輸出**：全局大綱儲存。

#### 2.8 章節發展牌陣
- **功能描述**：為每章生成結構。
- **牌陣**：起承轉合 + 場景（五張牌）
- **輸入**：
  - 章節數量：根據長度分配
  - 主角參與：多選框（優先顯示系列IP角色）
- **選擇項**：
  - 分支數量：2-3個
- **輸出**：每章牌陣結果儲存。

---

### 3. 內容生成模組
#### 3.1 標題生成
- **功能描述**：生成標題。
- **輸入**：核心概念牌陣 + 作家風格。
- **選擇項**：
  - 建議數量：3個標題
- **輸出**：標題儲存。

#### 3.2 世界觀內容生成
- **功能描述**：生成世界觀描述。
- **輸入**：世界觀牌陣 + 用戶設定 + 作家風格。
- **輸出**：文字段落（約200-500字）。

#### 3.3 角色內容生成
- **功能描述**：生成角色描述，優先處理系列IP角色。
- **輸入**：
  - 角色定位牌陣
  - 系列IP資料（若使用）
  - 作家風格
- **選擇項**：
  - 重新生成：按鈕
- **輸出**：角色描述（約200字），IP角色保持標誌性特質。

#### 3.4 章節內容生成
- **功能描述**：生成章節內容，突出系列IP角色。
- **輸入**：
  - 章節發展牌陣
  - 主角選擇（IP角色優先）
  - 分支路徑
  - 故事資料庫
  - 作家風格
- **選擇項**：
  - 分支路徑：下拉選單
  - 主角焦點：下拉選單
- **輸出**：每章文字（約1500-2000字）。

---

### 4. 結果調整模組
#### 4.1 重新生成
- **功能描述**：重新生成內容。
- **輸入**：
  - 目標部分：下拉選單（標題、世界觀、角色、章節）
- **輸出**：更新內容。

#### 4.2 手動編輯
- **功能描述**：簡單文字編輯。
- **輸入**：用戶修改文字。
- **輸出**：更新儲存內容。

#### 4.3 儲存與導出
- **功能描述**：儲存並導出故事。
- **選擇項**：
  - 格式：PDF、Word
- **輸出**：本地檔案與Firestore備份。

---

### 5. 連貫性管理模組
#### 5.1 故事資料庫
- **功能描述**：記錄世界觀、角色（含系列IP成長）、事件。
- **輸入**：自動提取生成內容。
- **輸出**：資料庫更新。

#### 5.2 連貫性檢查
- **功能描述**：檢測系列IP角色行為與設定一致性。
- **輸入**：生成內容 + IP資料。
- **輸出**：警告訊息。

---

### 使用範例
1. **創建系列IP「衛斯理」**：
   - 姓名：衛斯理
   - 外貌：黑髮藍眼，披風飄逸
   - 背景：孤兒，被神秘導師收養
   - 性格：勇敢但衝動，內心渴望認同
   - 目標：探索未知，尋找身世真相
   - 標誌性特質：手握短劍微笑
   - 儲存並用於奇幻長篇，生成「衛斯理傳奇：初探秘境」。

2. **從故事提取並升級**：
   - 故事生成角色「艾琳」，勇敢女騎士。
   - 提取後補充：外貌「紅髮飛揚」、標誌性行為「吹口哨」，成為系列IP。
   - 新故事「艾琳傳奇：龍之試煉」繼續發展。

3. **成長軌跡**：
   - 「衛斯理」在第一故事找到神劍，更新IP資料：更自信，目標新增「守護世界」。

---

### 總結
- **細緻設定**：IP角色新增背景深度、性格複雜性、目標層次、偶像化特質，適合系列故事。
- **系列應用**：支援一開始創建或故事中提取，並記錄成長軌跡，確保長期發展。
這設計讓IP角色成為「衛斯理傳奇」般的系列主角，甚至具備偶像魅力。如果需要更多細節或調整，請告訴我！