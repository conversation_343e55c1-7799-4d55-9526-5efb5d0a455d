# 資料庫結構說明

## 抽牌結構
- 一個欄位有多項目都是使用抽牌結構紀錄，後面一律紀錄為抽牌
	- 牌陣名稱
	- 抽牌：
		- 牌種：塔羅、盧恩
		- 含義：這個位置的卡牌代表意義
		- 卡牌：卡牌的帶還
		- 正逆：正位/逆位
	- 解讀：AI解讀結果可編輯

## 作者 author
- 使用者ID
- 作者姓名
- 參考作品
- 作品風格：AI依照參考作品產出，用於描述個人寫作風格
- LLM設定：陣列
	- 服務商名
	- 服務商IP
	- 使用模型

## 共同角色 sharedRole
- 共同角色ID
- 使用者ID
- 物種：人類、動物、擬人動物....
- 姓名：必填，可提供AI創造功能
- 年齡：數字
- 生日：選填
- 註記：選填，這個角色的基本設定
- 外貌：必填，可提供AI創造功能
- 背景資料：抽牌-關鍵事件、親人影響、技能來源、轉折點
- 性格特徵：抽牌-優點、缺點、內在衝突、慾望、恐懼
- 目標層次：抽牌-表面目標、深層目標、潛意識目標
- 標誌性特質：抽牌-標誌性行為、口號或語癖、魅力點
- 角色生日：
	- 分析所有設定資料，用占星術或八字設定人物的出生日期
	- 由所設定日期反推人物的設定，甚至大運
	- 這有助於強化IP的獨特性
- 經歷：陣列
	- 作品ID
	- 角色重要經歷及最終時間點狀態

## 共同物品 sharedItem
- 共同物品ID
- 使用者ID
- 名稱
- 外貌：長文字描述
- 典故：長文字描述
- 經歷：陣列
	- 作品ID
	- 角色重要經歷及最終時間點狀態

## 牌陣 spread
- 牌陣ID
- 牌陣名稱
- 卡牌：陣列。例如（（塔羅，過去）（塔羅，現在）（塔羅，未來）（盧恩，情境））
	- 牌種
	- 含義

## 作品風格
- 風格名稱
- 風格描述

## 作品
### 作品設定
- 作品ID
- 使用者ID
- 故事名稱：先設定，之後可以用AI創造
- 作品風格：（我的風格、預設風格）
- 目標讀者：（青少年、成人、通用）
- 地點：⌨️（古代、現代、未來、修仙、自訂）
- 長度：（短篇1000字、中篇5000字、長篇2萬字+）
- 世界觀：抽牌。
- 故事風格：🃏⌨️（幽默、嚴肅、詩意、自訂）
- 故事類型：🃏⌨️（奇幻、科幻、愛情、懸疑）
- 故事背景：⌨️（歷史事件、神話傳說、科學概念、地理、歷史、文化）
- 故事目標：⌨️（描述預想的結果，劇情最終發展結果）
- 主線大綱：抽牌-起承轉合
- 章節數量：由作品長度及主線大綱，由AI建議
- 作品狀態：手稿、預覽、完稿

### 作品角色
- 作品ID
- 陣列：
	- 角色代號：用於描述章節參與角色
	- 共同角色：sharedRoleID
	- 自創角色：與sharedRole相同的欄位內容
- 人物關係描述：⌨️

### 作品物品
- 作品ID
- 陣列：
	- 共同物品：sharedItemID
	- 自創角色：與sharedItem相同的欄位內容

### 作品章節
- 主角參與：陣列
- 物品參與：陣列
- **牌陣**：抽牌-起承轉合 + 場景（五張牌）
- 可以提供修改意見，再重新產生
- 摘要：保存章節摘要，供情節發展連續性參考

### 審查評論
- 審查或評論角色名稱：
	- 邏輯審查員
	- 文學評論家
	- 類型小說愛好者（依故事設定）
	- 普通讀者
	- 編輯或出版商
	- 心理學家或社會學家
	- 中國短劇觀眾
- 評論內容