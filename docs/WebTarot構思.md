# 構思

  

## 網站目的

-  提供塔羅牌和盧恩符文的占卜功能
- 目標客戶為全球，故需要為多語文介面（初步為中文和英文）
- 提供AI解讀功能，幫助用戶理解其占卜結果
- 這是一個收費的網站，用戶需要註冊並支付費用才能使用所有功能
- 可以免費匿名使用牌意查詢功能
## 網站功能

> 所有頁面功能詳細內容存放於每個頁面相對資料夾的MD檔案中

- 登錄頁面
- 功能選擇頁面
- 塔羅牌牌意查詢
- 塔羅牌占卜及AI解讀
	- 五張牌陣占卜
	- 元辰宮占卜
	- 評分牌陣
	- 自訂牌陣
- 盧恩符文查詢
	- 盧恩符文占卜及AI解讀
	- 三張符文陣占卜
	- 元辰宮占卜

  

## 主要技術

- 前端：React + Next.js
- 後端：Firebase 全系列（Firestore、Authentication、Functions、Storage、Hosting）
- 資料庫：Firestore
- 會讓使用者輸入AI API KEY，並將其儲存在Firestore中
- 身份驗證：Firebase Authentication
- 函數：Firebase Functions
- 存儲：Firebase Storage
	- 長文字如AI解讀結果，會以檔案型態儲存在Firebase Storage中
- 主機：Firebase Hosting
- 支付：綠界金流

### AI解讀技術

- 用戶輸入的占卜資訊包含問題、牌陣、抽牌等保存至firestore文件中
- 服務端監看firestore集合，當有解讀需求時服務端取得文件內容，並將其傳送至AI模型進行解讀
- 解讀完畢由解讀程式寫入解讀檔案路徑，並將路徑寫回原文件中
- 用戶可以在前端透過路徑取得解讀結果

### 服務端技術

- 服務端技術會另開專案管理 python 相關程式
- 使用 n8n 監看 firestore 集合，當呼叫 python 程式進行文件正規化，並呼叫AI解讀
- 服務端使用LLM+RAG技術，由RAG提供專業知識，並由LLM生成解讀結果
- 暫定使用 LM Studio+AnythinLLM 工具

## 檔案結構

- 這個網站必須符合自適應寬度及多國語言功能
- lib/pages之下為每一個主要功能頁面開設獨立的資料夾
- 主要功能頁面使用完整獨立的檔案命名
- 主要功能的次要頁面如果不被外部叫用，則使用簡要命名，例如：list_page, detail_page
- 主要功能中可以包含不被外部叫用的部件，存放於widgets資料夾中
- 其他不被外部叫用的功能都可以另開資料夾存放，例如：utils, services
- 頁面大小分S,M,L,XL，分別存為獨立檔案，大小為檔名後綴，初步開發以S為主，其他先不要做
- 每一個功能頁面有獨立的PRD文件
## 牌陣

### 牌陣顯示

- 依照牌陣需要顯示空位，並顯示牌位意義（若有的話）
- 牌陣數量可能需要分多行顯示（自動折行）
### 建議牌

- 可以在基本牌陣解讀後增加一張建議牌
- 建議牌也是從牌堆中抽取，故不會與基本牌陣重複
### 快速翻牌

- 顯示牌陣設定數量的牌背
- 按下牌背隨機從牌堆中抽取一張牌
- 抽取後牌背消失，顯示牌面
- 重複上述動作直到所有牌都抽取完畢
### 攤開翻牌

- 目的在於在線占卜時，讓遠端使用者依號碼選牌
- 備妥牌陣所需的空白位置等待抽取
- 將所有牌洗牌後依序排列，牌背並顯示順序的編號
- 抽取後顯示牌面，並不得再翻轉。並將所抽牌佔據空白位置。
- 重複上述動作直到所有牌都抽取完畢
### 分類選牌

- 用於使用者使用實體牌占卜時紀錄
- 備妥牌陣所需的空白位置等待抽取
- 直接依照分類顯示牌面，選取後翻轉為牌背，並將所抽牌佔據空白位置。
- 抽取可以取消，但取消牌之後的牌自動填補空白
- 重複上述動作直到所有牌都抽取完畢

### 評分牌陣

- 評分牌陣是一種特殊的牌陣，用於評估多個選項的好壞
- 評分牌陣需要先設定選項內容，例如：就讀哪個大學好？A,B,C,D
- 評分牌陣可以是一張牌，也可以是三張牌
- 依照每個選項所抽取的牌陣對問題的影響進行評分及評註，用戶再依據評分結果做出抉擇