# 使用 Visual Studio Code 維護 Firebase Hosting 網頁及 Cloud Functions

## 介紹

Visual Studio Code (VSCode) 是一個功能強大的編輯器，適合用於維護 Firebase Hosting 網頁和 Cloud Functions。使用 VSCode，您可以輕鬆管理專案代碼，進行調試，並保持良好的開發效率。

## 環境設置

### 前提條件

- 已安裝 Node.js 和 npm
- Firebase CLI
- Firebase 帳號
- Visual Studio Code

### 1. 初始化 Firebase 專案

在終端中導航到您的專案目錄，並運行以下命令以初始化 Firebase 專案：

```bash
firebase init
````

選擇要設置的 Hosting 和 Functions，然後按照提示進行操作。

## 在 VSCode 中維護 Firebase Hosting 網頁

### 2. 編輯 Hosting 網頁

在 VSCode 中，您會看到專案結構，其中包含 `public` 目錄，該目錄包含您的靜態網站文件。

#### 常見文件

- `index.html`：主頁面
- `styles.css`：樣式文件
- `script.js`：功能文件

#### 編輯步驟

1. 在 VSCode 中打開 `public` 目錄。
2. 編輯 `index.html`、`styles.css` 和 `script.js` 文件，添加或修改內容。
3. 使用 VSCode 的即時預覽插件（如 Live Server）進行本地測試，確保網站正常運行。

### 3. 部署 Hosting 更新

完成編輯後，使用終端運行以下命令以部署更新：

```bash
firebase deploy --only hosting
```

這將把您的更改上傳到 Firebase Hosting。

## 在 VSCode 中維護 Cloud Functions

### 4. 編輯 Cloud Functions

在 VSCode 中，導航到 `functions` 目錄，這裡包含了您的 Cloud Functions 代碼。

#### 常見文件

- `index.js` 或 `index.ts`：主函數文件

#### 編輯步驟

1. 在 VSCode 中打開 `functions/index.js` 或 `index.ts` 文件。
2. 編輯或添加 Cloud Functions，例如：

```javascript
const functions = require('firebase-functions');

exports.helloWorld = functions.https.onRequest((request, response) => {
    response.send("Hello, World!");
});
```

### 5. 本地測試 Cloud Functions

使用終端在 `functions` 目錄中運行以下命令，以啟動本地模擬器：

```bash
firebase emulators:start --only functions
```

在本地瀏覽器中測試函數，確保其運行正常。

### 6. 部署 Cloud Functions 更新

完成編輯和測試後，使用終端運行以下命令以部署 Cloud Functions 更新：

```bash
firebase deploy --only functions
```

## 維護和管理程式碼

### 7. 版本控制

使用 Git 來管理您的專案版本。可以在 VSCode 中直接使用 Git 集成功能，進行版本控制和團隊協作。

### 8. 插件支援

在 VSCode 中，安裝相關插件以增強開發體驗，例如：

- **Firebase**：方便管理 Firebase 服務。
- **Prettier**：自動格式化代碼。
- **ESLint**：檢查代碼質量和風格問題。

## 結論

使用 Visual Studio Code 維護 Firebase Hosting 網頁及 Cloud Functions，您可以高效地編輯、測試和部署代碼，並使用 Git 進行版本控制。VSCode 的豐富插件生態系統還能進一步提升開發效率。若需進一步的幫助，請參考 Firebase 和 VSCode 的官方文檔。
