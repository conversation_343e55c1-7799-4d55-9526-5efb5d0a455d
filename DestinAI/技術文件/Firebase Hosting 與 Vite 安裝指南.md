以下是針對你想要設置 Firebase Hosting 並使用 Vite 的完整步驟，包含基本概念及安裝指引，讓你可以順利開始。

---

# **Firebase Hosting 與 Vite 安裝指南**

### **簡介**

本指南介紹如何在本機環境設置 Firebase Hosting 與 Vite，並將 Vite 用作前端專案的建構工具。Firebase Hosting 是一個快速、可靠的靜態網站托管平台，而 Vite 則是現代化的前端構建工具，能夠提升開發體驗和效能。

---

### **基本概念**

1. **Firebase Hosting**：是一個提供靜態網站托管服務的平臺，可以用來部署 HTML、CSS、JavaScript 等靜態檔案。
2. **Vite**：是現代前端開發工具，專為提升開發效能而設計，能夠快速編譯並打包你的應用程式。

---

### **安裝步驟**

#### **步驟 1：安裝 Firebase CLI**

Firebase CLI 是用來與 Firebase 服務互動的命令行工具。

1. 使用 npm 安裝 Firebase CLI：
    
    ```bash
    npm install -g firebase-tools
    ```
    
2. 登入 Firebase：
    
    ```bash
    firebase login
    ```
    

---

#### **步驟 2：初始化 Firebase 專案**

在專案目錄中初始化 Firebase，這樣可以設定 Firebase 的服務，包括 Hosting。

1. 在終端機中進入專案資料夾（或創建一個新資料夾）：
    
    ```bash
    mkdir my-firebase-project
    cd my-firebase-project
    ```
    
2. 初始化 Firebase 專案：
    
    ```bash
    firebase init
    ```
    
    選擇 **Hosting**，並設置相關選項（如專案名稱、預設網站目錄等）。
    
    在 **public/** 資料夾（或後續設定的目錄）中會放置最終部署的靜態檔案。
    

---

#### **步驟 3：安裝 Vite**

Vite 是用來構建前端專案的工具。

1. 創建一個新的 Vite 專案（假設使用 React 作為範例）：
    
    ```bash
    npm create vite@latest frontend -- --template react
    ```
    
    這會在 `frontend` 資料夾中創建一個新的 Vite 專案。
    
2. 進入 `frontend` 資料夾並安裝依賴：
    
    ```bash
    cd frontend
    npm install
    ```
    

---

#### **步驟 4：設定 Firebase Hosting**

Firebase Hosting 預設會使用 `public/` 資料夾作為靜態檔案的來源，這裡需要修改設定以指向 Vite 的輸出目錄。

1. 在專案根目錄下的 `firebase.json` 文件中，將 `"public"` 目錄指向 Vite 輸出檔案的資料夾 `frontend/dist`，並進行必要的配置：
    
    ```json
    {
      "hosting": {
        "public": "frontend/dist",
        "ignore": ["firebase.json", "**/.*", "**/node_modules/**"],
        "rewrites": [
          { "source": "**", "destination": "/index.html" }
        ]
      }
    }
    ```
    

---

#### **步驟 5：編譯與部署**

1. 在 `frontend` 目錄中，使用 Vite 編譯前端專案：
    
    ```bash
    npm run build
    ```
    
2. 執行 Firebase 部署命令，將編譯後的檔案上傳至 Firebase Hosting：
    
    ```bash
    firebase deploy
    ```
    

---

### **總結**

現在，你已經完成了 Firebase Hosting 和 Vite 的安裝與設定，可以透過以下步驟開始部署你的前端應用程式：

1. 使用 Firebase CLI 初始化 Firebase 專案。
2. 使用 Vite 創建並編譯前端專案。
3. 設定 Firebase Hosting 指向 Vite 輸出的 `dist/` 目錄。
4. 編譯並部署應用程式。

當你修改 `frontend` 目錄中的程式碼並執行 `npm run build`，你可以再次執行 `firebase deploy` 部署更新的內容。

這樣你就能在 Firebase Hosting 上成功托管你的應用程式了！

---

希望這份指南對你有幫助，讓你能順利完成設置！如果有任何問題，隨時告訴我！😊