## 1. 簡介

Firebase Cloud Functions 是一種 **無伺服器（Serverless）** 的運算環境，可用來處理後端邏輯，並與 Firebase 其他服務（如 Firestore、Auth）整合。

### **適用場景**

- 監聽 Firestore、Auth、Storage 事件並執行後端邏輯
- 提供 API 給前端存取，處理 HTTP 請求
- 定時執行排程任務（如清理資料、發送通知）
- 與第三方 API（如支付、AI）整合

## 2. 主要功能

### **2.1 HTTP 觸發**

提供 API 端點，供前端或其他系統存取。

```javascript
const functions = require("firebase-functions");

exports.helloWorld = functions.https.onRequest((req, res) => {
    res.send("Hello from Firebase Cloud Functions!");
});
```

### **2.2 Firestore 觸發**

監聽 Firestore 資料變更，自動執行後端邏輯。

```javascript
exports.onUserCreated = functions.firestore
    .document("users/{userId}")
    .onCreate((snap, context) => {
        const newUser = snap.data();
        console.log("New user created:", newUser);
        return null;
    });
```

### **2.3 Authentication 觸發**

用戶註冊時執行後端邏輯，如發送歡迎郵件。

```javascript
exports.sendWelcomeEmail = functions.auth.user().onCreate((user) => {
    console.log(`Welcome email sent to ${user.email}`);
    return null;
});
```

### **2.4 定時排程**

使用 Cloud Scheduler 設定定時任務。

```javascript
exports.scheduledFunction = functions.pubsub.schedule('every 24 hours').onRun((context) => {
    console.log("This function runs every 24 hours!");
    return null;
});
```

## 3. 部署與管理

### **3.1 安裝 Firebase CLI**

```bash
npm install -g firebase-tools
firebase login
firebase init functions
```

### **3.2 部署函數**

```bash
firebase deploy --only functions
```

### **3.3 查看函數日誌**

```bash
firebase functions:log
```

## 4. 成本與限制

- **免費層**：每月 **200 萬次呼叫**，超過則按量計費。
- **冷啟動（Cold Start）**：標準方案存在冷啟動延遲，使用 **預熱機制** 可改善。
- **區域性**：建議選擇離主要用戶較近的區域（如 `us-central1`）。

## 5. 最佳實踐

- **避免冷啟動**：將函數設定為 **預熱**（如定期呼叫保持活躍）。
- **減少 Firestore 讀寫次數**：批量處理資料，降低成本。
- **使用環境變數**：避免將敏感資訊硬編碼。

```bash
firebase functions:config:set api.key="your_api_key"
```

## 6. 進階應用

- **整合 AI**：可透過 Cloud Functions 連接本地 **Ollama LLM**，提供 AI 推理服務。
- **串接支付系統**：使用 Cloud Functions 連接 **綠界**、**Stripe** 處理交易。
- **即時通知**：透過 Firebase Messaging 發送推播。

📌 **結論：Firebase Cloud Functions 適合無伺服器應用，適用於 API、事件處理及排程任務，與 Firebase 其他服務高度整合，能有效降低開發與維運成本。**