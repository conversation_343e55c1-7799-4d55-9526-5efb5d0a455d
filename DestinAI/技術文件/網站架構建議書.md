## **1. 總體架構概述**

### **1.1 架構選擇**

本網站將採用 **GitHub Pages** 作為前端託管，並使用 **Firebase** 提供後端服務，包括 **身份驗證（Auth）**、**資料庫（Firestore）**、**雲端函式（Cloud Functions）** 等。此外，本地端 **Ollama LLM 伺服器** 提供 AI 運算，透過 **Cloudflare Tunnel** 或 **DDNS** 讓 Firebase 可存取。

### **1.2 技術棧**

- **前端（Frontend）**：React（TypeScript） + GitHub Pages
- **後端（Backend）**：Firebase（Cloud Functions + Firestore）
- **AI 模型**：Ollama LLM（本地端運行）
- **網域名稱管理**：Cloudflare 或 DDNS

## **2. 各部分技術詳細說明**

### **2.1 前端架構**

#### **技術選擇**

- **框架**：React（TypeScript）
- **部署方式**：GitHub Pages（免費）
- **UI 框架**：ShadCN + Tailwind CSS
- **API 介接**：使用 Firebase API 存取後端資料

#### **運作流程**

1. 用戶造訪 GitHub Pages 上的網站。
2. 若需要登入，使用 Firebase Auth（如 Google、Email 登入）。
3. 使用者操作時，透過 Firebase API 讀取 Firestore 資料庫。
4. 若使用 AI 功能，前端向 Firebase Cloud Functions 發送請求，Cloud Functions 轉發至本地 Ollama LLM 伺服器。

---

### **2.2 後端架構（Firebase）**

#### **使用的 Firebase 服務**

- **Firebase Authentication**：管理用戶登入
- **Firestore Database**：儲存用戶資料、占卜歷史記錄等
- **Cloud Functions**：作為 API 轉發，讓 Firebase 存取本地 Ollama 伺服器

#### **運作流程**

1. 用戶登入 Firebase Auth。
2. 讀取 Firestore 內的記錄。
3. 觸發 Cloud Functions，將請求轉送到本地 Ollama LLM。
4. 回傳 AI 解析結果至 Firestore，供前端顯示。

---

### **2.3 AI 模型（Ollama LLM）**

#### **架設方式**

- 在本地運行 **Ollama LLM**（可用 M2 Mac）
- 透過 **Cloudflare Tunnel** 或 **DDNS** 讓 Firebase 存取本地伺服器

#### **存取方式**

1. **Cloudflare Tunnel**（推薦）：
    - 透過 Cloudflare 代理請求，不需開放家用 IP
    - HTTPS 內建，安全性高
    - Firebase 透過 `https://ollama.yourdomain.workers.dev/api/generate` 取得 AI 回應
2. **DDNS + Port Forwarding**（次選）：
    - 註冊免費 DDNS，將本地伺服器綁定一個固定的網域
    - 需開放家用網路的 Port，安全性較低

---

### **2.4 網域名稱管理**

#### **方案 1：Cloudflare Tunnel（推薦）**

- **優點**：
    - 無須開放路由器 Port，安全性高
    - 提供免費 HTTPS
- **缺點**：
    - 需要在本機運行 Cloudflare Tunnel 服務

#### **方案 2：DDNS + Port Forwarding**

- **優點**：
    - 免費取得網域名稱（如 FreeDNS、DuckDNS）
- **缺點**：
    - 需開放家用網路 Port（可能有安全風險）
    - 可能不支援 HTTPS

## **3. 成本分析**

|項目|選擇方案|費用|
|---|---|---|
|**前端託管**|GitHub Pages|免費|
|**後端（API）**|Firebase Cloud Functions|免費（免費層）|
|**資料庫**|Firestore|免費（免費層）|
|**身份驗證**|Firebase Auth|免費（基本用量）|
|**AI 模型**|本地 Ollama|免費（使用自有設備）|
|**網域名稱**|Cloudflare Tunnel|免費|
|**網域名稱（付費選項）**|Namecheap 或 Google Domains|約 $1~10/年|

### **3.1 使用者數量估算成本**

|使用者數量|Firebase 成本|AI 運算成本（本地端）|總成本|每用戶成本|
|---|---|---|---|---|
|**1,000**|$0（免費層）|$0（本地運行）|$0|$0|
|**10,000**|約 $20/月|$0（本地運行）|約 $20|$0.002|
|**100,000**|約 $200/月|$0（本地運行）|約 $200|$0.002|

## **4. 總結與建議**

|需求|解決方案|優勢|
|---|---|---|
|**免費前端託管**|GitHub Pages|簡單易用、免費|
|**後端 API**|Firebase Cloud Functions|免維護、高可用性|
|**數據存儲**|Firestore|自動擴展、即時同步|
|**身份驗證**|Firebase Auth|內建 Google、Email 登入|
|**AI 運算**|本地 Ollama LLM + Cloudflare Tunnel|低成本、高效能|
|**網域管理**|Cloudflare Tunnel|內建 HTTPS，安全性高|

**📌 最終建議：使用 Cloudflare Tunnel 來存取本地 Ollama，並透過 Firebase Cloud Functions 轉發請求，以確保安全性與穩定性。**