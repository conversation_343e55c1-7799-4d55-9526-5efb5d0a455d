## 1. 環境概述

本指南適用於使用 **Firebase Hosting + Firestore + Cloud Functions**，並搭配 **Vite** 來開發前端網站的 Monorepo 架構。此環境包含：

- **正式網站 (`website`)**：部署到 Firebase Hosting
- **本地管理後台 (`admin`)**：僅在本機開發，存取 Firestore 和 Functions
- **共享函式庫 (`packages/utils`)**
- **Cloud Functions (`functions`)**
- **PNPM 作為套件管理工具**

## 2. 初始化專案

### 2.1 安裝必要工具

請確保你的系統已安裝以下工具：

- **Node.js** (建議使用 LTS 版本)
- **PNPM**（安裝方式：`npm install -g pnpm`）
- **Firebase CLI**（安裝方式：`npm install -g firebase-tools`）

### 2.2 建立專案結構

```sh
mkdir my-project && cd my-project
pnpm init
mkdir apps packages functions
```

專案結構應如下：

```
my-project/
│── apps/
│   ├── website/  # Hosting 主要網站
│   ├── admin/    # 本地開發的管理後台
│── functions/    # Firebase Functions
│── packages/
│   ├── utils/    # 共用函式庫
│── firebase.json
│── pnpm-workspace.yaml
```

### 2.3 初始化 Firebase

```sh
firebase init
```

**選擇的功能**：

- Hosting: Deploy a web app
- Firestore Database
- Cloud Functions

選擇 **`apps/website`** 為 Hosting 目錄。

### 2.4 設定 PNPM Workspaces

在 `pnpm-workspace.yaml` 中定義工作區：

```yaml
packages:
  - "apps/*"
  - "packages/*"
  - "functions"
```

### 2.5 初始化 Vite 專案

```sh
cd apps/website
pnpm create vite . --template react  # 可依需求選擇框架
pnpm install
```

同樣地，`apps/admin` 也可依需求初始化。

### 2.6 設定 Firebase Hosting 使用 Vite

修改 `firebase.json` 讓 Hosting 指向 `dist/`：

```json
{
  "hosting": {
    "public": "apps/website/dist",
    "ignore": ["firebase.json", "**/.*", "**/node_modules/**"]
  }
}
```

## 3. 啟動開發環境

### 3.1 啟動本地網站

```sh
cd apps/website
pnpm run dev
```

### 3.2 部署至 Firebase Hosting

```sh
pnpm run build
firebase deploy --only hosting
```

### 3.3 啟動本地 Cloud Functions

```sh
cd functions
firebase emulators:start
```

## 4. 結論

此環境架構適用於 Monorepo，利用 PNPM Workspaces 統一管理依賴，讓正式網站與本地後台可以共用 Firestore 和 Functions。如果後續需要擴充，可在 `packages/` 添加更多共用模組，並使用 Lerna 進一步管理多個 NPM package。