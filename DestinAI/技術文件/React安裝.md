安裝 React 有幾種方式，以下提供幾種常見且簡單的方法：

**1. 使用 Create React App (推薦新手使用)**

Create React App 是一個由 Facebook（現 Meta）官方提供的工具，可以快速建立一個 React 專案，並自動配置好開發環境。

- **先決條件：**
    - Node.js：請先確保您的電腦已安裝 Node.js。您可以從 Node.js 官網下載並安裝：[https://nodejs.org/](https://www.google.com/url?sa=E&source=gmail&q=https://nodejs.org/)
    - npm（Node Package Manager）或 yarn：Node.js 安裝時會自動安裝 npm。yarn 則是另一個套件管理器，您可以選擇使用。
- **安裝步驟：**
    1. 開啟終端機（Windows 使用命令提示字元或 PowerShell，macOS 使用終端機）。
    2. 執行以下指令來建立一個新的 React 專案（將 `my-app` 替換為您的專案名稱）：
        - 使用 npm：`npx create-react-app my-app`
        - 使用 yarn：`yarn create react-app my-app`
    3. 等待安裝完成。
    4. 進入專案目錄：`cd my-app`
    5. 啟動開發伺服器：`npm start` 或 `yarn start`
    6. 您的瀏覽器會自動開啟，並顯示 React 應用程式。

**2. 使用 Vite**

Vite 是一個快速的前端構建工具，也可以用來建立 React 專案。

- **先決條件：**
    - Node.js：請先確保您的電腦已安裝 Node.js。
    - npm 或 yarn。
- **安裝步驟：**
    1. 開啟終端機。
    2. 執行以下指令來建立一個新的 Vite React 專案（將 `my-react-app` 替換為您的專案名稱）：
        - 使用 npm: `npm create vite@latest my-react-app --template react`
        - 使用 yarn: `yarn create vite my-react-app --template react`
    3. 進入專案目錄：`cd my-react-app`
    4. 安裝依賴：`npm install` 或 `yarn install`
    5. 啟動開發伺服器：`npm run dev` 或 `yarn dev`

**3. 直接在 HTML 中引入 React**

這種方式適合簡單的範例或快速測試，不建議用於大型專案。

- **安裝步驟：**
    
    1. 建立一個 HTML 檔案。
    2. 在 `<head>` 標籤中引入 React 和 ReactDOM 的 CDN 連結：
    

<!-- end list -->

HTML

```
<script src="https://unpkg.com/react@17/umd/react.development.js"></script>
<script src="https://unpkg.com/react-dom@17/umd/react-dom.development.js"></script>
```

1. 在 `<body>` 標籤中建立一個用於渲染 React 元件的 DOM 元素：

<!-- end list -->

HTML

```
<div id="root"></div>
```

1. 在 `<body>` 標籤的結尾處，使用 `<script>` 標籤編寫 React 程式碼：

<!-- end list -->

HTML

```
<script>
  ReactDOM.render(
    React.createElement('h1', null, 'Hello, React!'),
    document.getElementById('root')
  );
</script>
```

**建議：**

- 對於初學者，強烈建議使用 Create React App 或 Vite，因為它們可以簡化開發環境的配置。
- 如果您想更深入了解 React 的底層原理，可以嘗試直接在 HTML 中引入 React。

希望這些資訊對您有幫助！