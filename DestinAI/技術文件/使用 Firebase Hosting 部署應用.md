## 介紹

本文件將指導您如何將應用部署到 Firebase Hosting，這是一個快速、靜態網站託管服務，適合前端應用和靜態網站。

## 前提條件

- 已安裝 Node.js 和 npm
- Firebase CLI
- Firebase 帳號

## 步驟

### 1. 初始化 Firebase

在您的專案目錄中，運行以下命令以初始化 Firebase：

```bash
firebase init
1. 選擇 **Hosting** 選項，然後選擇要使用的 Firebase 項目。
2. 設定為將應用部署到 `public` 目錄，並選擇重寫為 `index.html`，如果您的應用是單頁應用。

### 2. 添加內容到專案

在 `public` 目錄中，創建或編輯以下文件：

- `index.html`：應用的主頁面。
- `styles.css`：樣式文件。
- `script.js`：功能文件。

範例 `index.html` 文件：

```html
<!DOCTYPE html>
<html lang="zh-Hant">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>我的 Firebase Hosting 應用</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <h1>歡迎來到我的 Firebase Hosting 應用！</h1>
    <script src="script.js"></script>
</body>
</html>
```

### 3. 部署到 Firebase Hosting

在專案根目錄下運行以下命令以進行部署：

```bash
firebase deploy
```

這將上傳您的應用到 Firebase Hosting。部署完成後，您會獲得一個公開的 URL，通過該 URL 訪問您的應用。

### 4. 測試應用

在瀏覽器中打開部署後的 URL，檢查您的應用是否正常運行。如需進行任何修改，請在 `public` 目錄中編輯文件，然後再次運行 `firebase deploy` 部署更新。

## 結論

您已成功將應用部署到 Firebase Hosting。這種方法使您能夠輕鬆地構建和分享靜態網站或前端應用。如需進一步的幫助，請參考 Firebase 的官方文檔。
