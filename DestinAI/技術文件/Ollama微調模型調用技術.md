以下是關於如何使用 Firebase Cloud Functions 通過 Cloudflare Tunnel 調用本地 Ollama LLM 的技術文件：

```markdown
# 使用 Firebase Cloud Functions 透過 Cloudflare Tunnel 調用本地 Ollama LLM

## 介紹

本文件將指導您如何設置 Firebase Cloud Functions，通過 Cloudflare Tunnel 安全地調用本地的 Ollama LLM（大語言模型）。這種配置允許您利用 Firebase 的無伺服器架構，並在本地執行模型，從而提供靈活的部署選項。

## 前提條件

- 已安裝 Node.js 和 npm
- Firebase CLI
- Cloudflare 帳號
- 已安裝 Ollama LLM 並在本地運行
- Cloudflare Tunnel 已設置並配置

## 步驟

### 1. 設置 Cloudflare Tunnel

1. 登錄到您的 Cloudflare 帳號。
2. 創建一個新的 Tunnel：
   - 在 Cloudflare 儀表板中，導航到 **Zero Trust** > **Access** > **Tunnels**。
   - 點擊 **Create a Tunnel**，並按照指示進行操作。

3. 配置 Tunnel，以便將流量轉發到您本地的 Ollama LLM 服務。假設您的 LLM 在本地的端口 `5000` 上運行，您需要將 Tunnel 配置為指向 `http://localhost:5000`。

4. 記下 Tunnel 的公共 URL，將在 Firebase Cloud Functions 中使用。

### 2. 設置 Firebase Cloud Functions

1. 在您的專案中初始化 Firebase：
   ```bash
   firebase init functions
```

1. 進入 `functions` 目錄並安裝 `axios` 用於發送 HTTP 請求：
    
    ```bash
    cd functions
    npm install axios
    ```
    
3. 編輯 `index.js` 文件，添加以下代碼以調用本地 Ollama LLM：
    
    ```javascript
    const functions = require('firebase-functions');
    const axios = require('axios');
    
    // Cloudflare Tunnel 的公共 URL
    const ollamaUrl = 'https://your-tunnel-url.cloudflare.com';
    
    exports.callOllamaLLM = functions.https.onRequest(async (req, res) => {
        try {
            const prompt = req.body.prompt; // 從請求中獲取提示
            const response = await axios.post(`${ollamaUrl}/api/generate`, { prompt });
            
            res.status(200).send(response.data); // 返回 LLM 的響應
        } catch (error) {
            console.error('Error calling Ollama LLM:', error);
            res.status(500).send('Error calling Ollama LLM');
        }
    });
    ```
    

### 3. 部署 Firebase Cloud Functions

1. 部署您的 Firebase Cloud Functions：
    
    ```bash
    firebase deploy --only functions
    ```
    
2. 部署完成後，您將獲得一個 Cloud Functions 的 URL。
    

### 4. 測試設置

使用 Postman 或其他 HTTP 客戶端發送請求到 Cloud Functions 的 URL，並包含您的提示。例如：

- **HTTP 方法**：POST
- **URL**：`https://your-cloud-function-url.cloudfunctions.net/callOllamaLLM`
- **Body**：
    
    ```json
    {
        "prompt": "Tell me a story about a brave knight."
    }
    ```
    

### 5. 檢查結果

查看 Cloud Functions 的響應，您應該會收到來自本地 Ollama LLM 的生成結果。

## 結論

通過以上步驟，您已成功設置了 Firebase Cloud Functions，以通過 Cloudflare Tunnel 調用本地的 Ollama LLM。這使得您可以在無伺服器環境中輕鬆使用強大的 LLM，同時確保安全和靈活的架構。

如需進一步的幫助或遇到問題，請參考相關文檔或社區資源。

```

這篇技術文件包含了設置和調用 Ollama LLM 的詳細步驟，幫助您快速實現功能。如果有任何進一步的問題或需要調整的地方，請隨時告訴我！
```